<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' https://unpkg.com; script-src 'self' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline' https://unpkg.com; font-src 'self'; img-src 'self' data: https://*.tile.openstreetmap.org"
    />
    <title>SMCA GSA Dashboard</title>
    <link rel="stylesheet" href="styles.css" />
    <link rel="stylesheet" href="loading-indicator.css" />
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="assets/leaflet/leaflet.css" />
    <!-- Font Awesome for icons (local) -->
    <link rel="stylesheet" href="assets/fontawesome/css/font-face.css" />
    <link rel="stylesheet" href="assets/fontawesome/css/all.min.css" />
  </head>
  <body>
    <!-- Loading Indicator Template (will be cloned by JS) -->
    <template id="loading-indicator-template">
      <div class="loading-container">
        <div class="loading-text">
          <span class="loading-letter">G</span>
          <span class="loading-letter">.</span>
          <span class="loading-letter">S</span>
          <span class="loading-letter">.</span>
          <span class="loading-letter">A</span>
        </div>
      </div>
    </template>

    <!-- Database Connection Error Screen -->
    <div class="db-error-screen" id="db-error-screen">
      <div class="db-error-content">
        <div class="db-error-icon">
          <i class="fas fa-database"></i>
          <i class="fas fa-times db-error-x"></i>
        </div>
        <h2>Veuillez vérifier la connexion à la base de données</h2>
        <p id="db-error-message">
          Impossible de se connecter au serveur de base de données.
        </p>
        <button id="retry-connection" class="action-btn">Réessayer</button>
      </div>
    </div>

    <div class="app-container" id="app-container">
      <!-- Floating lateral tabs -->
      <div class="lateral-tabs" id="lateral-tabs">
        <div class="tab active" data-tab="dashboard">
          <i class="fas fa-tachometer-alt"></i>
          <span class="tab-tooltip">Dashboard</span>
        </div>
        <div class="tab" data-tab="map">
          <i class="fas fa-map-marked-alt"></i>
          <span class="tab-tooltip">Map</span>
        </div>
        <div class="tab" data-tab="list">
          <i class="fas fa-list-ul"></i>
          <span class="tab-tooltip">List</span>
        </div>
        <div class="tab" data-tab="actions">
          <i class="fas fa-tasks"></i>
          <span class="tab-tooltip">Actions</span>
        </div>
        <!-- Toggle button for collapsing/expanding tabs -->
        <div class="tabs-toggle" id="tabs-toggle">
          <i class="fas fa-chevron-left"></i>
        </div>
      </div>

      <!-- Content area -->
      <div class="content-area">
        <!-- Dashboard Tab Content -->
        <div class="tab-content active" id="dashboard-content">
          <h1>Dashboard</h1>

          <!-- iPhone-style Toggle Buttons Row -->
          <div class="dashboard-toggle-container">
            <div class="dashboard-toggle-buttons">
              <div class="toggle-slider"></div>
              <button class="dashboard-toggle-btn active" data-view="planning">Planning</button>
              <button class="dashboard-toggle-btn" data-view="timeline">KPIs • Formations</button>
              <button class="dashboard-toggle-btn" data-view="analytics">KPIs • Stagiaires</button>
              <button class="dashboard-toggle-btn" data-view="overview">Overview</button>
              <button class="dashboard-toggle-btn" data-view="reports">Recommendations</button>
            </div>
          </div>

          <!-- Main Dashboard View (Planning + Timeline) -->
          <div class="dashboard-main-view" id="dashboard-main-view">
            <!-- Planning Schedule - Horizontal Scrollable Calendar -->
          <div class="planning-schedule-container">
            <!-- Control buttons container -->
            <div class="timeline-controls">
              <!-- Left section: Current Year Display -->
              <div class="timeline-left">
                <div id="current-year-display" class="current-year-display">2025</div>
              </div>

              <!-- Center section: Drag indicator -->
              <div class="timeline-center">
                <div class="drag-indicator">
                  <i class="fas fa-arrow-left"></i>
                  <i class="fas fa-hand-pointer"></i>
                  <i class="fas fa-arrow-right"></i>
                </div>
              </div>

              <!-- Right section: Buttons -->
              <div class="timeline-right">
                <button id="refresh-btn" class="timeline-btn refresh-btn" title="Rafraîchir les données">
                  <i class="fas fa-sync-alt"></i>
                </button>
                <button id="current-week-btn" class="timeline-btn current-week-btn" title="Aller à la semaine actuelle">
                  <i class="fas fa-calendar-day"></i>
                </button>
              </div>
            </div>

            <div class="planning-schedule" id="planning-schedule">
              <!-- Week columns will be generated by JavaScript -->
            </div>
            <!-- Fixed training count display -->
            <div id="active-trainings-count" class="active-trainings-count">00 formations en cours</div>
          </div>

          <!-- Activities Timeline - Horizontal Timeline -->
          <div class="activities-timeline-container">
            <div class="activities-timeline" id="activities-timeline">
              <!-- Activities will be generated by JavaScript -->
            </div>
          </div>

          </div>

          <!-- KPI Dashboard View (Statistics) -->
          <div class="dashboard-kpi-view" id="dashboard-kpi-view">
            <div class="dashboard-borderpane">
              <div class="kpi-container">
                <div class="loading-message">Chargement des statistiques...</div>
                <div class="kpi-row" style="display: none;">
                  <!-- Full page training statistics with domain selection -->
                  <div class="kpi-training-fullpage">
                    <!-- Domain selection circles - larger and including "Tout" -->
                    <div class="domain-selection-container" id="formations-domain-selection-container">
                      <div class="domain-circle active" data-domain="tout">
                        <span>Tout</span>
                      </div>
                      <!-- Domain circles will be populated dynamically -->
                    </div>

                    <!-- Training statistics display -->
                    <div class="training-stats-container">
                      <div class="training-stats-left">
                        <!-- Evolution chart -->
                        <div class="evolution-section">
                          <div class="evolution-title">Évolution des formations</div>
                          <div class="evolution-chart-container">
                            <canvas id="training-evolution-chart"></canvas>
                          </div>
                          <div class="evolution-percentage" id="evolution-percentage">0%</div>
                          <div class="evolution-label">Évolution annuelle</div>

                          <!-- Evolution details -->
                          <div class="evolution-details">
                            <div class="evolution-details-title">Analyse de tendance</div>
                            <div class="evolution-details-text" id="evolution-details-text">
                              Cette métrique montre l'évolution du nombre de formations par rapport à l'année précédente.
                              Une tendance positive indique une augmentation de l'activité de formation.
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="training-stats-right">
                        <!-- Total count and breakdown -->
                        <div class="training-count-section">
                          <!-- Top section with count and formations side by side -->
                          <div class="training-count-header">
                            <div class="training-count-left">
                              <div class="training-total-count" id="training-total-count">0</div>
                              <div class="training-total-label" id="training-total-label">Formations</div>
                            </div>

                            <div class="training-count-right">
                              <div class="formations-list-title clickable-title" id="formations-list-title">Liste des formations</div>
                              <div class="formations-list" id="formations-list">
                                <!-- Will be populated dynamically -->
                              </div>
                            </div>
                          </div>

                          <!-- Training summary -->
                          <div class="training-summary">
                            <div class="training-summary-title">Résumé des formations</div>
                            <div class="training-summary-text" id="training-summary-text">
                              Nombre total de formations disponibles dans le domaine sélectionné.
                            </div>
                          </div>

                          <!-- Training types breakdown -->
                          <div class="training-types-breakdown" id="training-types-breakdown">
                            <!-- Will be populated dynamically -->
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="error-message" style="display: none;">Erreur lors du chargement des statistiques</div>
              </div>
            </div>
          </div>

          <!-- Trainee KPI Dashboard View (Statistics) -->
          <div class="dashboard-trainee-kpi-view" id="dashboard-trainee-kpi-view">
            <div class="dashboard-borderpane">
              <div class="kpi-container">
                <div class="loading-message">Chargement des statistiques des stagiaires...</div>
                <div class="kpi-row" style="display: none;">
                  <!-- Full page trainee statistics with domain selection -->
                  <div class="kpi-training-fullpage">
                    <!-- Domain selection circles - larger and including "Tout" -->
                    <div class="domain-selection-container" id="trainee-domain-selection-container">
                      <div class="domain-circle active" data-domain="tout">
                        <span>Tout</span>
                      </div>
                      <!-- Domain circles will be populated dynamically -->
                    </div>

                    <!-- Trainee statistics display -->
                    <div class="training-stats-container">
                      <div class="training-stats-left">
                        <!-- Evolution chart -->
                        <div class="evolution-section">
                          <div class="evolution-title">Évolution des stagiaires</div>
                          <div class="evolution-chart-container">
                            <canvas id="trainee-evolution-chart"></canvas>
                          </div>
                          <div class="evolution-percentage" id="trainee-evolution-percentage">0%</div>
                          <div class="evolution-label">Évolution annuelle</div>
                          <div class="evolution-details" id="trainee-evolution-details-text">Chargement des données...</div>
                        </div>
                      </div>

                      <div class="training-stats-right">
                        <!-- Total count and breakdown -->
                        <div class="training-count-section">
                          <!-- Top section with count and types side by side -->
                          <div class="training-count-header">
                            <div class="training-count-left">
                              <div class="training-total-count" id="trainee-total-count">0</div>
                              <div class="training-total-label" id="trainee-total-label">Stagiaires</div>
                            </div>
                            <div class="training-count-right">
                              <!-- Top 3 unités -->
                              <div class="top-unites-section" id="top-unites-section">
                                <div class="top-unites-title">Top 3 Unités</div>
                                <div class="top-unites-list" id="top-unites-list">
                                  <!-- Will be populated dynamically -->
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Training summary -->
                          <div class="training-summary">
                            <div class="training-summary-title">Résumé des stagiaires</div>
                            <div class="training-summary-text" id="trainee-summary-text">
                              Nombre total de stagiaires dans le domaine sélectionné.
                            </div>
                          </div>

                          <!-- Training types breakdown -->
                          <div class="training-types-breakdown" id="trainee-types-breakdown">
                            <div class="training-types-title">Répartition par type</div>
                            <div class="training-types-list" id="trainee-types-list">
                              <!-- Will be populated dynamically -->
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="error-message" style="display: none;">Erreur lors du chargement des statistiques des stagiaires</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Map Tab Content -->
        <div class="tab-content" id="map-content">
          <h1>Map View</h1>
          <div class="map-container">
            <div id="morocco-map"></div>

            <!-- GSA Logo Overlay (shown when no toggle is active) -->
            <div class="map-logo-overlay" id="map-logo-overlay">
              <img src="gsa.png" alt="GSA Logo">
            </div>

            <!-- Floating toggle buttons for map data layers -->
            <div class="map-toggle-container">
              <button class="map-toggle-btn" data-layer="missiles">
                <i class="fas fa-rocket"></i>
                <span>Missiles</span>
              </button>
              <button class="map-toggle-btn" data-layer="drones">
                <i class="fas fa-plane"></i>
                <span>Drones</span>
              </button>
              <button class="map-toggle-btn" data-layer="personnel">
                <i class="fas fa-user-shield"></i>
                <span>Personnel drone</span>
              </button>
              <button class="map-toggle-btn" data-layer="stats">
                <i class="fas fa-chart-bar"></i>
                <span>Statistiques</span>
              </button>
            </div>

            <!-- Missions Panel (shown only when missiles toggle is active) -->
            <div class="missions-panel" id="missions-panel" style="display: none;">
              <div class="missions-header">
                <h3>Prochaines vérifications</h3>
              </div>
              <div class="missions-list" id="missions-list">
                <!-- Mission items will be populated here -->
              </div>
            </div>
          </div>
        </div>

        <!-- List Tab Content -->
        <div class="tab-content" id="list-content">
          <h1>Formations</h1>
          <div class="split-container">
            <!-- Panneau de gauche (liste des formations) -->
            <div class="split-panel left-panel" id="trainings-list-panel">
              <div class="panel-header">
                <div class="domain-filter" id="domain-filter">
                  <!-- La liste des domaines sera générée dynamiquement par JavaScript -->
                  <div class="loading-message">Chargement des domaines...</div>
                </div>
              </div>
              <div class="panel-content">
                <div class="trainings-list" id="trainings-list">
                  <!-- La liste des formations sera générée dynamiquement par JavaScript -->
                  <div class="loading-message">Chargement des formations...</div>
                </div>
              </div>
            </div>

            <!-- Séparateur vertical redimensionnable -->
            <div class="split-resizer vertical-resizer" id="vertical-resizer"></div>

            <!-- Panneau de droite (vue unique) -->
            <div class="split-panel right-panel" id="details-panel">
              <div class="panel-header">
                <h2 id="training-header">Veuillez sélectionner une formation</h2>
              </div>
              <div class="panel-content">
                <div class="training-details" id="training-details">
                  <div class="placeholder-message">Veuillez sélectionner une formation</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions Tab Content -->
        <div class="tab-content" id="actions-content">
          <h1>Actions List</h1>
          <div class="actions-container">
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-file-export"></i></div>
              <div class="action-details">
                <h3>Export Data</h3>
                <p>Export data to various formats</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-sync"></i></div>
              <div class="action-details">
                <h3>Refresh Data</h3>
                <p>Update all data sources</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-cog"></i></div>
              <div class="action-details">
                <h3>Settings</h3>
                <p>Configure application settings</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-user-cog"></i></div>
              <div class="action-details">
                <h3>User Management</h3>
                <p>Manage user accounts and permissions</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Logo and Brand Text -->
    <div class="app-footer">
      <div class="brand-container">
        <img src="gsa.png" alt="GSA Logo" class="brand-logo" />
        <span class="brand-separator">|</span>
        <span class="brand-text">Groupe de Soutien Artillerie</span>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <div class="search-bar">
        <input type="text" id="search-input" placeholder="Rechercher..." />
        <button id="search-button">
          <i class="fas fa-search"></i>
        </button>
      </div>
    </div>

    <!-- Date Time Display, Fullscreen and Theme Switcher -->
    <div class="bottom-controls">
      <div class="datetime-display" id="datetime-display">
        16 octobre 2025, 22h30
      </div>
      <div class="fullscreen-toggle" id="fullscreen-toggle">
        <i class="fas fa-expand"></i>
      </div>
      <div class="theme-switcher" id="theme-switcher">
        <i class="fas fa-sun"></i>
      </div>
    </div>

    <!-- Leaflet JS -->
    <script src="assets/leaflet/leaflet.js"></script>
    <!-- Chart.js -->
    <script src="assets/chart.js/chart.min.js"></script>
    <!-- Chart.js Datalabels Plugin -->
    <script src="https://unpkg.com/chartjs-plugin-datalabels@2"></script>
    <!-- Main Application Script -->
    <script src="renderer.js"></script>

    <!-- Dashboard Toggle Buttons Script -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize dashboard toggle buttons
        const toggleButtons = document.querySelectorAll('.dashboard-toggle-btn');
        const toggleSlider = document.querySelector('.dashboard-toggle-buttons .toggle-slider');

        if (toggleButtons.length > 0 && toggleSlider) {
          // Set initial slider position for the first (active) button
          updateSliderPosition(toggleButtons[0]);

          // Add click event listeners to all toggle buttons
          toggleButtons.forEach((button, index) => {
            button.addEventListener('click', function() {
              // Remove active class from all buttons
              toggleButtons.forEach(btn => btn.classList.remove('active'));

              // Add active class to clicked button
              this.classList.add('active');

              // Update slider position
              updateSliderPosition(this);

              // Handle view switching logic here
              handleViewSwitch(this.dataset.view);
            });
          });
        }

        function updateSliderPosition(activeButton) {
          const buttonRect = activeButton.getBoundingClientRect();
          const containerRect = activeButton.parentElement.getBoundingClientRect();
          const leftOffset = buttonRect.left - containerRect.left - 4; // Account for container padding
          const buttonWidth = buttonRect.width;

          toggleSlider.style.left = leftOffset + 'px';
          toggleSlider.style.width = buttonWidth + 'px';
        }

        function handleViewSwitch(view) {
          console.log('Switching to view:', view);

          const mainView = document.getElementById('dashboard-main-view');
          const kpiView = document.getElementById('dashboard-kpi-view');
          const traineeKpiView = document.getElementById('dashboard-trainee-kpi-view');

          // Hide all views first
          if (mainView) mainView.style.display = 'none';
          if (kpiView) kpiView.style.display = 'none';
          if (traineeKpiView) traineeKpiView.style.display = 'none';

          switch(view) {
            case 'planning':
              console.log('Showing planning view - two sections sharing height');
              if (mainView) {
                mainView.style.display = 'flex';
              }
              break;
            case 'timeline':
              console.log('Showing KPIs • Formations view');
              if (kpiView) {
                kpiView.style.display = 'flex';
                kpiView.style.flexDirection = 'column';
                kpiView.style.height = '100%';
                kpiView.style.marginTop = '40px';
                // Initialize formations statistics when view is shown
                window.initializeFormationsStatistics();
              }
              break;
            case 'analytics':
              console.log('Showing KPIs • Stagiaires view');
              if (traineeKpiView) {
                traineeKpiView.style.display = 'flex';
                traineeKpiView.style.flexDirection = 'column';
                traineeKpiView.style.height = '100%';
                traineeKpiView.style.marginTop = '40px';
                // Initialize trainee statistics when view is shown
                window.initializeTraineeStatistics();
              }
              break;
            case 'overview':
              console.log('Showing overview view');
              // Future implementation
              break;
            case 'reports':
              console.log('Showing recommendations view');
              // Future implementation
              break;
            default:
              console.log('Unknown view:', view);
              // Default to planning view
              if (mainView) {
                mainView.style.display = 'flex';
              }
          }
        }
      });
    </script>

    <!-- Direct Map Initialization Script -->
    <script>
      // This script directly initializes the map when the page loads
      document.addEventListener('DOMContentLoaded', function () {
        console.log('Direct map initialization script loaded');

        // Initialize map after a delay
        setTimeout(function () {
          console.log('Attempting direct map initialization');

          try {
            // Get the map element
            const mapElement = document.getElementById('morocco-map');
            if (!mapElement) {
              console.error('Map element not found in direct script');
              return;
            }

            console.log('Map element found in direct script:', mapElement);
            console.log(
              'Map element dimensions:',
              mapElement.offsetWidth,
              'x',
              mapElement.offsetHeight
            );

            // Force the map element to be visible
            mapElement.style.display = 'block';
            mapElement.style.height = '100%';
            mapElement.style.width = '100%';
            mapElement.style.position = 'relative';

            // Check if Leaflet is loaded
            if (typeof L === 'undefined') {
              console.error('Leaflet not loaded in direct script');
              return;
            }

            console.log('Leaflet is loaded in direct script');

            // Check if map is already initialized
            if (mapElement._leaflet_id) {
              console.log('Map already initialized in direct script');
              return;
            }

            // Define initial map center and zoom level
            const initialCenter = [28.5, -8.0]; // Further adjusted center to position Morocco even higher in the view
            const initialZoom = 6.0; // Initial zoom level set to 6 as requested

            // Define missile view constants (shared between missile view and reset button)
            const MISSILE_VIEW_CENTER = [32.4, -4.8372]; // Zagora area coordinates
            const MISSILE_VIEW_ZOOM = 7.0; // Missile view zoom level

            // Create the map
            console.log('Creating map in direct script...');
            var directMap = L.map('morocco-map', {
              center: initialCenter,
              zoom: initialZoom,
              minZoom: 5.0, // Minimum zoom level
              maxZoom: 10,
              zoomControl: true,
              attributionControl: false, // Hide attribution control
              zoomSnap: 0.10, // Allow finer zoom control
              zoomDelta: 0.5, // Smoother zoom steps
              wheelPxPerZoomLevel: 120 // More sensitive mouse wheel zooming
            });

            console.log('Map created in direct script');

            // Add map controls (reset button and zoom level display)
            addZoomLevelDisplay(directMap);

            // Load Morocco GeoJSON data
            console.log('Loading Morocco GeoJSON data...');

            // Define style for the GeoJSON - simple style for Morocco boundaries
            function style(feature) {
              return {
                fillColor: '#333333', // dodgerblue
                weight: 0.5,
                opacity: 1,
                color: 'white',
                fillOpacity: 0.6,
                smoothFactor: 1,
              };
            }

            // Simple feature handler - no interactions
            function onEachFeature(feature, layer) {
              // No interactions needed, just display the boundaries
            }

            // Create a variable to hold the GeoJSON layer
            var moroccoGeoJSON;

            // For testing error handling - set to true to simulate error
            var simulateError = false;

            // Fetch the GeoJSON file
            console.log('Fetching morocco.geojson file...');

            // Direct fetch of morocco.geojson as requested with cache-busting
            fetch('assets/data/morocco.geojson?v=' + new Date().getTime())
              .then(function (response) {
                console.log('GeoJSON fetch response status:', response.status);
                if (!response.ok) {
                  throw new Error(
                    'Network response was not ok: ' + response.status
                  );
                }
                console.log('Parsing GeoJSON response...');
                return response.json();
              })
              .then(function (data) {
                console.log(
                  'GeoJSON data loaded successfully, features:',
                  data.features ? data.features.length : 'none'
                );

                try {
                  // Add the GeoJSON layer to the map
                  console.log('Creating GeoJSON layer...');
                  moroccoGeoJSON = L.geoJSON(data, {
                    style: style,
                    onEachFeature: onEachFeature,
                  });

                  console.log('Adding GeoJSON layer to map...');
                  moroccoGeoJSON.addTo(directMap);

                  console.log('Fitting map to GeoJSON bounds...');
                  directMap.fitBounds(moroccoGeoJSON.getBounds());

                  console.log('Morocco GeoJSON added to map successfully');

                  // Legend removed as requested

                  // Buttons removed as requested

                  // Fetching and displaying drone deployments disabled as requested
                  console.log('Drone fetching and display disabled as requested');

                  // Set up map toggle buttons
                  setupMapToggleButtons();
                } catch (err) {
                  console.error('Error processing GeoJSON data:', err);
                  throw err;
                }
              })
              .catch(function (error) {
                console.error('Error loading GeoJSON:', error);

                // Display an error message in the map container
                console.log(
                  'Displaying error message for GeoJSON load failure'
                );

                // Create an error message element
                var errorDiv = document.createElement('div');
                errorDiv.className = 'map-error-message';
                errorDiv.innerHTML = `
                  <div class="error-icon"><i class="fas fa-exclamation-triangle"></i></div>
                  <h3>Erreur de chargement de la carte</h3>
                  <p>Impossible de charger les données cartographiques du Maroc.</p>
                  <p class="error-details">Détails: ${error.message}</p>
                  <p class="error-path">Chemin du fichier: assets/data/morocco.geojson</p>
                `;

                // Clear the map container and add the error message
                var mapContainer = document.getElementById('morocco-map');

                if (!mapContainer) {
                  console.error('Map container not found for error message');
                  return;
                }

                console.log('Map container found, adding error message');

                // Create a centered container for the error message
                var centerDiv = document.createElement('div');
                centerDiv.className = 'map-error-container';
                centerDiv.appendChild(errorDiv);

                // Add the error container to the map
                try {
                  mapContainer.innerHTML = '';
                  mapContainer.appendChild(centerDiv);

                  // Set background color
                  mapContainer.style.backgroundColor = 'var(--bg-secondary)';

                  console.log('Error message added to map container');
                } catch (err) {
                  console.error(
                    'Error adding error message to map container:',
                    err
                  );
                }
              });

            // Global variable to store the drone layer group
            let droneLayerGroup = null;

            // Function to fetch and display drone deployments
            async function fetchAndDisplayDroneDeployments(map) {
              console.log('Fetching drone deployments...');

              // Restore default map settings for drone view
              restoreDefaultMapSettings(map);

              // Show loading indicator
              const mapContainer = document.querySelector('.map-container');
              const loadingIndicator = window.showLoading(mapContainer);

              try {
                // Fetch drone deployments from the database
                const response = await window.api.getDroneDeployments();

                // Add a small delay to ensure the loading animation is visible
                await new Promise(resolve => setTimeout(resolve, 800));

                // Hide loading indicator
                window.hideLoading(mapContainer);

                if (!response.success) {
                  console.error('Error fetching drone deployments:', response.error);
                  alert('Error fetching drone data: ' + response.error);
                  return;
                }

                const deployments = response.data;
                console.log('Drone deployments fetched:', JSON.stringify(deployments));
                console.log(`Total drone records: ${deployments.length}`);

                // Remove existing drone layer group if it exists
                if (droneLayerGroup) {
                  map.removeLayer(droneLayerGroup);
                }

                // Create a new layer group for all drone markers
                droneLayerGroup = L.layerGroup().addTo(map);

                // Clear the drone markers array
                droneMarkers = [];

                // Check if we have any deployments
                if (!deployments || deployments.length === 0) {
                  console.warn('No drone deployments found in database');
                  alert('No drone deployments found in database');
                  return;
                }

                // Process each drone deployment
                for (let i = 0; i < deployments.length; i++) {
                  const drone = deployments[i];

                  console.log(`Processing drone ${i+1}/${deployments.length}: ID=${drone.id}, Type=${drone.type_drone}, Position=[${drone.lat}, ${drone.lng}]`);

                  // Validate drone data
                  if (!drone.lat || !drone.lng || !drone.type_drone) {
                    console.warn(`Skipping drone ${i+1} due to missing data:`, drone);
                    continue;
                  }

                  // Convert lat/lng to numbers if they're strings
                  const lat = typeof drone.lat === 'string' ? parseFloat(drone.lat) : drone.lat;
                  const lng = typeof drone.lng === 'string' ? parseFloat(drone.lng) : drone.lng;

                  // Store the drone position for fitting bounds
                  droneMarkers.push([lat, lng]);

                  // Create marker for the drone position - simple grey circle
                  const droneMarker = L.circleMarker([lat, lng], {
                    radius: 5, // Small circle
                    fillColor: '#808080', // Grey for all markers
                    color: '#fff',
                    weight: 1, // Thin border
                    opacity: 1,
                    fillOpacity: 0.8
                  }).addTo(droneLayerGroup);

                  // Calculate optimal label position to avoid overlaps
                  const labelPosition = calculateLabelPosition(lat, lng, droneMarkers, i);

                  // Get the calculated label position
                  const labelLat = labelPosition.lat;
                  const labelLng = labelPosition.lng;

                  // Create a label with the simplified information
                  const labelIcon = L.divIcon({
                    className: 'drone-label',
                    html: `<div class="drone-label-content">
                      <div class="drone-city">${drone.ville_implantation || 'N/A'}</div>
                      <div class="drone-info">
                        ${drone.unite || 'N/A'}
                      </div>
                      <div class="drone-type-container">
                        <span class="drone-type">${drone.type_drone || 'N/A'}</span>
                      </div>
                    </div>`,
                    iconSize: [220, 65], // Size for three lines with styling
                    iconAnchor: [0, 30]  // Adjusted anchor point
                  });

                  // Add the label marker at the offset position
                  const labelMarker = L.marker([labelLat, labelLng], {
                    icon: labelIcon,
                    zIndexOffset: 1000 // Ensure labels are above other markers
                  }).addTo(droneLayerGroup);

                  // Create a subtle line connecting the marker to the label
                  const connectorLine = L.polyline(
                    [
                      [lat, lng],
                      [labelLat, labelLng] // Use calculated offset position
                    ],
                    {
                      color: 'var(--text-secondary)', // Use theme color
                      weight: 0.8, // Very thin line
                      opacity: 0.5, // Subtle
                      dashArray: '2,4'
                    }
                  ).addTo(droneLayerGroup);

                  // Add popup with simplified drone details
                  droneMarker.bindPopup(`
                    <div class="drone-popup">
                      <h3>Drone #${drone.id}</h3>
                      <p>Type: ${drone.type_drone || 'N/A'}</p>
                      <p>Unité: ${drone.unite || 'N/A'}</p>
                      <p>Ville: ${drone.ville_implantation || 'N/A'}</p>
                    </div>
                  `);

                  console.log(`Added drone marker ${i+1} to map`);
                }

                console.log(`Displayed ${droneMarkers.length} drone markers on map`);

                // Automatic refresh disabled as requested
                console.log('Automatic refresh disabled as requested');
              } catch (error) {
                console.error('Error displaying drone deployments:', error);
                alert('Error displaying drone deployments: ' + error.message);
              }
            }

            // Variable to store the refresh interval
            let droneRefreshInterval = null;

            // Function to set up automatic refresh of drone positions
            function setupDroneRefresh(map) {
              // Clear any existing interval
              if (droneRefreshInterval) {
                clearInterval(droneRefreshInterval);
              }

              // Set up new interval to refresh drone positions every 30 seconds
              droneRefreshInterval = setInterval(async () => {
                console.log('Refreshing drone positions...');
                try {
                  // Fetch drone deployments from the database
                  const response = await window.api.getDroneDeployments();

                  if (!response.success) {
                    console.error('Error refreshing drone deployments:', response.error);
                    return;
                  }

                  const deployments = response.data;

                  // Remove existing drone layer group
                  if (droneLayerGroup) {
                    map.removeLayer(droneLayerGroup);
                  }

                  // Create a new layer group for all drone markers
                  droneLayerGroup = L.layerGroup().addTo(map);

                  // Clear the drone markers array
                  droneMarkers = [];

                  // Check if we have any deployments
                  if (!deployments || deployments.length === 0) {
                    console.warn('No drone deployments found in database during refresh');
                    return;
                  }

                  // Process each drone deployment
                  for (let i = 0; i < deployments.length; i++) {
                    const drone = deployments[i];

                    // Validate drone data
                    if (!drone.lat || !drone.lng || !drone.type_drone) {
                      console.warn(`Skipping drone ${i+1} during refresh due to missing data:`, drone);
                      continue;
                    }

                    // Convert lat/lng to numbers if they're strings
                    const lat = typeof drone.lat === 'string' ? parseFloat(drone.lat) : drone.lat;
                    const lng = typeof drone.lng === 'string' ? parseFloat(drone.lng) : drone.lng;

                    // Store the drone position for fitting bounds
                    droneMarkers.push([lat, lng]);

                    // Create marker for the drone position - simple grey circle
                    const droneMarker = L.circleMarker([lat, lng], {
                      radius: 5, // Small circle
                      fillColor: '#808080', // Grey for all markers
                      color: '#fff',
                      weight: 1, // Thin border
                      opacity: 1,
                      fillOpacity: 0.8
                    }).addTo(droneLayerGroup);

                    // Calculate optimal label position to avoid overlaps
                    const labelPosition = calculateLabelPosition(lat, lng, droneMarkers, i);

                    // Get the calculated label position
                    const labelLat = labelPosition.lat;
                    const labelLng = labelPosition.lng;

                    // Create a label with the simplified information
                    const labelIcon = L.divIcon({
                      className: 'drone-label',
                      html: `<div class="drone-label-content">
                        <div class="drone-city">${drone.ville_implantation || 'N/A'}</div>
                        <div class="drone-info">
                          ${drone.unite || 'N/A'}
                        </div>
                        <div class="drone-type-container">
                          <span class="drone-type">${drone.type_drone || 'N/A'}</span>
                        </div>
                      </div>`,
                      iconSize: [220, 65], // Size for three lines with styling
                      iconAnchor: [0, 30]  // Adjusted anchor point
                    });

                    // Add the label marker at the offset position
                    const labelMarker = L.marker([labelLat, labelLng], {
                      icon: labelIcon,
                      zIndexOffset: 1000 // Ensure labels are above other markers
                    }).addTo(droneLayerGroup);

                    // Create a subtle line connecting the marker to the label
                    const connectorLine = L.polyline(
                      [
                        [lat, lng],
                        [labelLat, labelLng] // Use calculated offset position
                      ],
                      {
                        color: 'var(--text-secondary)', // Use theme color
                        weight: 0.8, // Very thin line
                        opacity: 0.5, // Subtle
                        dashArray: '2,4'
                      }
                    ).addTo(droneLayerGroup);

                    // Add popup with simplified drone details
                    droneMarker.bindPopup(`
                      <div class="drone-popup">
                        <h3>Drone #${drone.id}</h3>
                        <p>Type: ${drone.type_drone || 'N/A'}</p>
                        <p>Unité: ${drone.unite || 'N/A'}</p>
                        <p>Ville: ${drone.ville_implantation || 'N/A'}</p>
                      </div>
                    `);
                  }

                  console.log(`Refreshed ${droneMarkers.length} drone markers on map`);

                  console.log('Drone positions refreshed');
                } catch (error) {
                  console.error('Error refreshing drone positions:', error);
                }
              }, 30000); // Refresh every 30 seconds
            }

            // Helper function to get color based on drone type
            function getDroneColor(droneType) {
              const colorMap = {
                'Surveillance': '#1E90FF', // DodgerBlue
                'Reconnaissance': '#32CD32', // LimeGreen
                'Attack': '#FF4500', // OrangeRed
                'Transport': '#FFD700', // Gold
                'Medical': '#FF69B4', // HotPink
              };

              return colorMap[droneType] || '#808080'; // Default to gray if type not found
            }

            // Global function to restore default map settings for non-missile views
            function restoreDefaultMapSettings(map) {
              // Default settings (original map settings)
              const defaultCenter = [31.7917, -7.0926]; // Morocco default center
              const defaultZoom = 5; // Default zoom level

              console.log('Restoring default map view: center =', defaultCenter, 'zoom =', defaultZoom);

              // Only restore if we're coming from missile view
              if (map._currentView === 'missiles') {
                map.setView(defaultCenter, defaultZoom);

                // Remove missile legend when leaving missile view
                const existingLegend = document.querySelector('.missile-legend');
                if (existingLegend) {
                  existingLegend.remove();
                  console.log('Removed missile legend');
                }
              }

              // Update current view
              map._currentView = 'default';
            }

            // Global function to restore default map settings for non-missile views
            function restoreDefaultMapSettings(map) {
              // Default settings (original map settings)
              const defaultCenter = [31.7917, -7.0926]; // Morocco default center
              const defaultZoom = 5; // Default zoom level

              console.log('Restoring default map view: center =', defaultCenter, 'zoom =', defaultZoom);

              // Only restore if we're coming from missile view
              if (map._currentView === 'missiles') {
                map.setView(defaultCenter, defaultZoom);
              }

              // Update current view
              map._currentView = 'default';
            }

            // Helper function to calculate optimal label position to avoid overlaps
            function calculateLabelPosition(lat, lng, allMarkers, index, defaultOffset = 0.15) {
              // Define possible positions around the circle (8 directions)
              const positions = [
                { lat: defaultOffset, lng: 0 },            // North
                { lat: defaultOffset, lng: defaultOffset }, // Northeast
                { lat: 0, lng: defaultOffset },            // East
                { lat: -defaultOffset, lng: defaultOffset }, // Southeast
                { lat: -defaultOffset, lng: 0 },           // South
                { lat: -defaultOffset, lng: -defaultOffset }, // Southwest
                { lat: 0, lng: -defaultOffset },           // West
                { lat: defaultOffset, lng: -defaultOffset }  // Northwest
              ];

              // If this is the first marker or there are no other markers, use default Northeast position
              if (allMarkers.length <= 1 || index === 0) {
                return {
                  lat: lat + positions[1].lat,
                  lng: lng + positions[1].lng
                };
              }

              // Calculate distances to all other markers
              const distances = [];
              for (let i = 0; i < allMarkers.length; i++) {
                if (i !== index) {
                  const otherLat = allMarkers[i][0];
                  const otherLng = allMarkers[i][1];
                  const distance = Math.sqrt(
                    Math.pow(lat - otherLat, 2) +
                    Math.pow(lng - otherLng, 2)
                  );
                  distances.push({ index: i, distance });
                }
              }

              // Sort by distance (closest first)
              distances.sort((a, b) => a.distance - b.distance);

              // If closest marker is far enough away, use default position
              if (distances.length === 0 || distances[0].distance > defaultOffset * 3) {
                return {
                  lat: lat + positions[1].lat,
                  lng: lng + positions[1].lng
                };
              }

              // Get the closest marker
              const closestMarker = allMarkers[distances[0].index];
              const closestLat = closestMarker[0];
              const closestLng = closestMarker[1];

              // Determine which direction to place the label based on relative position
              // of the closest marker
              let bestPosition;

              if (closestLat > lat && closestLng > lng) {
                // Closest marker is Northeast, so place label Southwest
                bestPosition = positions[5];
              } else if (closestLat > lat && closestLng < lng) {
                // Closest marker is Northwest, so place label Southeast
                bestPosition = positions[3];
              } else if (closestLat < lat && closestLng > lng) {
                // Closest marker is Southeast, so place label Northwest
                bestPosition = positions[7];
              } else if (closestLat < lat && closestLng < lng) {
                // Closest marker is Southwest, so place label Northeast
                bestPosition = positions[1];
              } else if (closestLat > lat) {
                // Closest marker is North, so place label South
                bestPosition = positions[4];
              } else if (closestLat < lat) {
                // Closest marker is South, so place label North
                bestPosition = positions[0];
              } else if (closestLng > lng) {
                // Closest marker is East, so place label West
                bestPosition = positions[6];
              } else {
                // Closest marker is West, so place label East
                bestPosition = positions[2];
              }

              return {
                lat: lat + bestPosition.lat,
                lng: lng + bestPosition.lng
              };
            }

            // Function to fetch and display missile module deployments
            async function fetchAndDisplayMissileDeployments(map) {
              console.log('Fetching missile deployments...');

              // Show loading indicator
              const mapContainer = document.querySelector('.map-container');
              const loadingIndicator = window.showLoading(mapContainer);

              try {
                // Fetch missile deployments from the database
                const response = await window.api.getMissileDeployments();

                // Add a small delay to ensure the loading animation is visible
                await new Promise(resolve => setTimeout(resolve, 800));

                // Hide loading indicator
                window.hideLoading(mapContainer);

                if (!response.success) {
                  console.error('Error fetching missile deployments:', response.error);
                  alert('Error fetching missile data: ' + response.error);
                  return;
                }

                const deployments = response.data;
                console.log('Missile deployments fetched:', JSON.stringify(deployments));
                console.log(`Total missile records: ${deployments.length}`);

                // Remove existing missile layer group if it exists
                if (missileLayerGroup) {
                  map.removeLayer(missileLayerGroup);
                }

                // Create a new layer group for all missile markers
                missileLayerGroup = L.layerGroup().addTo(map);

                // Clear the missile markers array
                missileMarkers = [];

                // Check if we have any deployments
                if (!deployments || deployments.length === 0) {
                  console.warn('No missile deployments found in database');
                  alert('No missile deployments found in database');
                  return;
                }



                // Set custom zoom and center for missile view only
                const setMissileViewSettings = (map, markers) => {
                  console.log('Setting custom missile view: center =', MISSILE_VIEW_CENTER, 'zoom =', MISSILE_VIEW_ZOOM);

                  // Set the custom view for missiles using shared constants
                  map.setView(MISSILE_VIEW_CENTER, MISSILE_VIEW_ZOOM);
                  // Store that this is missile view for future reference
                  map._currentView = 'missiles';
                };



                // Group deployments by location to handle overlapping markers
                const locationGroups = {};

                // First pass: group deployments by coordinates or ville_implantation
                deployments.forEach((missile, index) => {
                  // Validate missile data - only require lat/lng for plotting
                  if (!missile.lat || !missile.lng) {
                    console.warn(`Skipping missile ${index+1} due to missing coordinates:`, missile);
                    return;
                  }

                  // Convert lat/lng to numbers if they're strings
                  const lat = typeof missile.lat === 'string' ? parseFloat(missile.lat) : missile.lat;
                  const lng = typeof missile.lng === 'string' ? parseFloat(missile.lng) : missile.lng;

                  const ville = missile.ville_implantation || 'unknown';

                  // Create a key based on coordinates (rounded to avoid floating point issues) or ville
                  const coordKey = `${lat.toFixed(4)}_${lng.toFixed(4)}`;
                  const locationKey = `${ville}_${coordKey}`;

                  if (!locationGroups[locationKey]) {
                    locationGroups[locationKey] = [];
                  }
                  locationGroups[locationKey].push({ missile, originalIndex: index, lat, lng });
                });

                // Second pass: process each location group with spacing
                Object.keys(locationGroups).forEach(locationKey => {
                  const group = locationGroups[locationKey];

                  group.forEach((item, groupIndex) => {
                    const { missile, originalIndex, lat, lng } = item;

                    console.log(`Processing missile ${originalIndex+1}/${deployments.length}: ID=${missile.id}, Group=${groupIndex+1}/${group.length}`);

                    // Calculate offset for overlapping markers
                    let offsetLat = lat;
                    let offsetLng = lng;

                    if (group.length > 1) {
                      // Create circular arrangement for multiple markers at same location
                      const radius = 0.003; // Small radius for spacing (about 300m)
                      const angle = (groupIndex * 2 * Math.PI) / group.length;
                      offsetLat = lat + (radius * Math.cos(angle));
                      offsetLng = lng + (radius * Math.sin(angle));
                      console.log(`Applied offset for overlapping marker: [${offsetLat}, ${offsetLng}]`);
                    }

                    // Store the missile position for fitting bounds
                    missileMarkers.push([offsetLat, offsetLng]);

                    // Check if this is a D.M type (ammunition) - use special icon
                    if (missile.type === 'D.M') {
                      // Create ammunition icon for D.M type - brown color, bigger size
                      const ammunitionIcon = L.divIcon({
                        className: 'ammunition-icon',
                        html: `
                          <div class="ammunition-marker">
                            <i class="fas fa-boxes" style="color: #8B4513; font-size: 18px; text-shadow: 0 0 3px rgba(0,0,0,0.6);"></i>
                          </div>
                        `,
                        iconSize: [24, 24],
                        iconAnchor: [12, 12]
                      });

                      const missileMarker = L.marker([offsetLat, offsetLng], {
                        icon: ammunitionIcon,
                        zIndexOffset: 100
                      }).addTo(missileLayerGroup);
                    } else {
                      // Regular circle marker for non-D.M types
                      const isLightTheme = document.body.classList.contains('light-theme');
                      const circleColor = missile.materiel === 'Sky Dragon' ? '#ff0000' : '#ffff00'; // Red for Sky Dragon, Yellow for others

                      // Create simple dot marker - smaller size with colored circle
                      const missileMarker = L.circleMarker([offsetLat, offsetLng], {
                        radius: 3, // Smaller dot
                        fillColor: isLightTheme ? '#808080' : '#ffffff',
                        color: circleColor, // Colored circle based on materiel
                        weight: 2, // Slightly thicker border for visibility
                        opacity: 1,
                        fillOpacity: 0.9
                      }).addTo(missileLayerGroup);
                    }

                    console.log(`Added missile marker ${originalIndex+1} to map with offset`);
                  });
                });

                console.log(`Displayed ${missileMarkers.length} missile markers on map`);

                // Add Sidi Yahya location pin (current location marker)
                const sidiYahyaCoords = [34.30494, -6.30404]; // Sidi Yahya, Morocco coordinates
                const sidiYahyaIcon = L.divIcon({
                  className: 'sidi-yahya-pin',
                  html: `
                    <div class="location-pin">
                      <i class="fas fa-map-marker-alt" style="color: #00ff00; font-size: 20px; text-shadow: 0 0 3px rgba(0,0,0,0.5);"></i>
                    </div>
                  `,
                  iconSize: [20, 20],
                  iconAnchor: [10, 20]
                });

                const sidiYahyaMarker = L.marker(sidiYahyaCoords, {
                  icon: sidiYahyaIcon,
                  zIndexOffset: 1000 // Ensure it appears above other markers
                }).addTo(missileLayerGroup);

                console.log('Added Sidi Yahya location pin to map');

                // Set custom zoom and center for missile view
                setMissileViewSettings(directMap, missileMarkers);

              } catch (error) {
                console.error('Error displaying missile deployments:', error);
                alert('Error displaying missile deployments: ' + error.message);
              }
            }

            // Legend function removed as requested

            // Global variable to store drone markers for fitting bounds
            let droneMarkers = [];

            // Global variables for missile markers
            let missileLayerGroup = null;
            let missileMarkers = [];

            // Function to fit the map to show all drone markers (kept for potential future use)
            function fitMapToDrones(map) {
              if (droneMarkers.length === 0) {
                console.log('No drone markers to fit');
                return;
              }

              // Create a bounds object
              const bounds = L.latLngBounds(droneMarkers);

              // Fit the map to the bounds with some padding
              map.fitBounds(bounds, {
                padding: [50, 50],
                maxZoom: 8
              });

              console.log('Map fitted to show all drones');
            }

            // Global variable for mission arrow layer
            let missionArrowLayer = null;

            // Function to show missions panel and fetch mission data
            async function showMissionsPanel() {
              const missionsPanel = document.getElementById('missions-panel');
              const missionsList = document.getElementById('missions-list');

              if (missionsPanel) {
                missionsPanel.style.display = 'block';
                console.log('Missions panel shown');

                try {
                  // Fetch mission data from database
                  const response = await window.api.getMissionVerificationMissiles();

                  if (response.success) {
                    console.log('Missions fetched successfully:', response.data);
                    displayMissions(response.data);

                    // Display arrow for first mission if exists
                    if (response.data.length > 0) {
                      console.log('First mission for arrow display:', response.data[0]);
                      displayMissionArrow(response.data[0]);
                    }
                  } else {
                    console.error('Failed to fetch missions:', response.error);
                    missionsList.innerHTML = `<div class="mission-item">Erreur: ${response.error}</div>`;
                  }
                } catch (error) {
                  console.error('Error fetching missions:', error);
                  missionsList.innerHTML = `<div class="mission-item">Erreur: ${error.message}</div>`;
                }
              }
            }

            // Function to hide missions panel
            function hideMissionsPanel() {
              const missionsPanel = document.getElementById('missions-panel');
              if (missionsPanel) {
                missionsPanel.style.display = 'none';
                console.log('Missions panel hidden');
              }
            }

            // Function to calculate time difference in French format
            function getTimeDifference(missionDate, today) {
              const mission = new Date(missionDate);
              const todayDate = new Date(today);
              const diffTime = mission - todayDate;
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

              if (diffDays === 0) {
                return "Aujourd'hui";
              } else if (diffDays === 1) {
                return "Demain";
              } else if (diffDays < 7) {
                return `Dans ${diffDays} jrs`;
              } else if (diffDays < 30) {
                const weeks = Math.floor(diffDays / 7);
                const remainingDays = diffDays % 7;
                if (remainingDays === 0) {
                  return `Dans ${weeks.toString().padStart(2, '0')} sem`;
                } else {
                  return `Dans ${weeks.toString().padStart(2, '0')} sem ${remainingDays.toString().padStart(2, '0')} jrs`;
                }
              } else {
                const months = Math.floor(diffDays / 30);
                const remainingDays = diffDays % 30;
                if (remainingDays === 0) {
                  return `Dans ${months.toString().padStart(2, '0')} mois`;
                } else if (remainingDays < 7) {
                  return `Dans ${months.toString().padStart(2, '0')} mois ${remainingDays} jrs`;
                } else {
                  const weeks = Math.floor(remainingDays / 7);
                  const finalDays = remainingDays % 7;
                  if (finalDays === 0) {
                    return `Dans ${months.toString().padStart(2, '0')} mois ${weeks.toString().padStart(2, '0')} sem`;
                  } else {
                    return `Dans ${months.toString().padStart(2, '0')} mois ${weeks.toString().padStart(2, '0')} sem ${finalDays} jrs`;
                  }
                }
              }
            }

            // Function to display missions in the panel
            function displayMissions(missions) {
              const missionsList = document.getElementById('missions-list');
              const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

              console.log('All missions received:', missions);
              console.log('Today date for filtering:', today);

              // Filter missions to show only current and future ones
              const filteredMissions = missions.filter(mission => {
                const missionDate = mission.rescheduled_date || mission.date;
                const isCurrentOrFuture = missionDate >= today;
                console.log(`Mission ${mission.id}: date=${missionDate}, isCurrentOrFuture=${isCurrentOrFuture}`);
                return isCurrentOrFuture;
              });

              console.log('Filtered missions (current/future only):', filteredMissions);

              // Sort missions: current missions first, then future missions by date
              const sortedMissions = filteredMissions.sort((a, b) => {
                const dateA = a.rescheduled_date || a.date;
                const dateB = b.rescheduled_date || b.date;

                const isCurrentA = dateA === today;
                const isCurrentB = dateB === today;

                // Current missions first
                if (isCurrentA && !isCurrentB) return -1;
                if (!isCurrentA && isCurrentB) return 1;

                // Within same category (current or future), sort by date
                return dateA.localeCompare(dateB);
              });

              console.log('Sorted missions (current first, then future):', sortedMissions);

              // Store sorted missions globally for hover functionality
              window.currentMissions = sortedMissions;

              if (sortedMissions.length === 0) {
                missionsList.innerHTML = '<div class="mission-item">Aucune mission programmée</div>';
                return;
              }

              // Group round-trip missions (using sorted missions)
              const groupedMissions = groupRoundTripMissions(sortedMissions);

              // Check if there are any current missions (today's missions)
              const currentMissions = sortedMissions.filter(mission => {
                const missionDate = mission.rescheduled_date || mission.date;
                return missionDate === today;
              });

              // Find the first future mission for time indicator
              let firstFutureMissionFound = false;
              let firstFutureMissionId = null;

              for (const group of groupedMissions) {
                for (const mission of group.missions) {
                  const missionDate = mission.rescheduled_date || mission.date;
                  if (missionDate > today && !firstFutureMissionFound) {
                    firstFutureMissionId = mission.id;
                    firstFutureMissionFound = true;
                    break;
                  }
                }
                if (firstFutureMissionFound) break;
              }

              // Add placeholder for no current missions if needed
              let placeholderHtml = '';
              if (currentMissions.length === 0) {
                placeholderHtml = '<div class="no-current-mission-placeholder">Aucune mission n\'est en cours</div>';
              }

              const missionsHtml = groupedMissions.map(group => {
                if (group.isRoundTrip) {
                  // Render round-trip group
                  return `
                    <div class="mission-group">
                      ${group.missions.map((mission, index) => {
                        const missionDate = mission.rescheduled_date || mission.date;
                        const isCurrent = missionDate === today;
                        const isFuture = missionDate > today;

                        const statusClass = isCurrent ? 'current' : (isFuture ? 'future' : '');
                        const tripType = index === 0 ? 'aller' : 'retour';

                        // Add time indicator for first future mission
                        const isFirstFuture = mission.id === firstFutureMissionId;
                        const timeIndicator = isFirstFuture ? `<div class="time-indicator">${getTimeDifference(missionDate, today)}</div>` : '';

                        return `
                          <div class="mission-item ${statusClass} ${tripType}"
                               data-mission-id="${mission.id}"
                               onmouseenter="showMissionArrowById('${mission.id}')"
                               onmouseleave="showDefaultArrow()"
                               onclick="toggleMissionSelection('${mission.id}')">
                            <div class="mission-date">${formatDate(missionDate)}</div>
                            <div class="mission-type">${mission.type || 'N/A'}</div>
                            ${timeIndicator}
                            <div class="mission-route">
                              <span>${mission.lieu_enlevement || 'N/A'}</span>
                              <span class="route-separator">→</span>
                              <span>${mission.lieu_acheminement || 'N/A'}</span>
                            </div>
                          </div>
                        `;
                      }).join('')}
                    </div>
                  `;
                } else {
                  // Render single mission
                  const mission = group.missions[0];
                  const missionDate = mission.rescheduled_date || mission.date;
                  const isCurrent = missionDate === today;
                  const isFuture = missionDate > today;

                  const statusClass = isCurrent ? 'current' : (isFuture ? 'future' : '');

                  // Add time indicator for first future mission
                  const isFirstFuture = mission.id === firstFutureMissionId;
                  const timeIndicator = isFirstFuture ? `<div class="time-indicator">${getTimeDifference(missionDate, today)}</div>` : '';

                  return `
                    <div class="mission-item ${statusClass}"
                         data-mission-id="${mission.id}"
                         onmouseenter="showMissionArrowById('${mission.id}')"
                         onmouseleave="showDefaultArrow()"
                         onclick="toggleMissionSelection('${mission.id}')">
                      <div class="mission-date">${formatDate(missionDate)}</div>
                      <div class="mission-type">${mission.type || 'N/A'}</div>
                      ${timeIndicator}
                      <div class="mission-route">
                        <span>${mission.lieu_enlevement || 'N/A'}</span>
                        <span class="route-separator">→</span>
                        <span>${mission.lieu_acheminement || 'N/A'}</span>
                      </div>
                    </div>
                  `;
                }
              }).join('');

              // Combine placeholder and missions
              missionsList.innerHTML = placeholderHtml + missionsHtml;
            }

            // Global variable to track selected mission
            window.selectedMissionId = null;

            // Function to show mission arrow on hover
            window.showMissionArrowById = function(missionId) {
              // Don't change arrow on hover if a mission is selected
              if (window.selectedMissionId) return;

              console.log('showMissionArrowById called with ID:', missionId);
              console.log('Available missions:', window.currentMissions);

              if (!window.currentMissions) {
                console.log('No currentMissions available');
                return;
              }

              const mission = window.currentMissions.find(m => m.id == missionId);
              console.log('Found mission:', mission);

              if (mission) {
                console.log('Displaying arrow for mission:', mission.lieu_enlevement, '→', mission.lieu_acheminement);
                displayMissionArrow(mission);
              } else {
                console.log('Mission not found with ID:', missionId);
              }
            };

            // Function to show default arrow (first mission)
            window.showDefaultArrow = function() {
              // Don't change arrow on hover if a mission is selected
              if (window.selectedMissionId) return;

              console.log('showDefaultArrow called');

              if (!window.currentMissions || window.currentMissions.length === 0) {
                console.log('No missions available for default arrow');
                return;
              }

              console.log('Showing default arrow for first mission:', window.currentMissions[0]);
              displayMissionArrow(window.currentMissions[0]);
            };

            // Function to toggle mission selection
            window.toggleMissionSelection = function(missionId) {
              console.log('toggleMissionSelection called with ID:', missionId);

              // Remove previous selection styling
              document.querySelectorAll('.mission-item.selected').forEach(item => {
                item.classList.remove('selected');
              });

              if (window.selectedMissionId === missionId) {
                // Deselect - go back to first mission
                window.selectedMissionId = null;
                console.log('Deselected mission, showing default arrow');
                if (window.currentMissions && window.currentMissions.length > 0) {
                  displayMissionArrow(window.currentMissions[0]);
                }
              } else {
                // Select new mission
                window.selectedMissionId = missionId;
                console.log('Selected mission ID:', missionId);

                // Add selected styling
                const selectedElement = document.querySelector(`[data-mission-id="${missionId}"]`);
                if (selectedElement) {
                  selectedElement.classList.add('selected');
                }

                // Show arrow for selected mission
                const mission = window.currentMissions.find(m => m.id == missionId);
                if (mission) {
                  displayMissionArrow(mission);
                }
              }
            };

            // Function to group round-trip missions
            function groupRoundTripMissions(missions) {
              const groups = [];
              const used = new Set();

              for (let i = 0; i < missions.length; i++) {
                if (used.has(i)) continue;

                const mission1 = missions[i];
                const date1 = new Date(mission1.rescheduled_date || mission1.date);

                // Look for return trip (next day, reversed route)
                let foundReturn = false;
                for (let j = i + 1; j < missions.length; j++) {
                  if (used.has(j)) continue;

                  const mission2 = missions[j];
                  const date2 = new Date(mission2.rescheduled_date || mission2.date);

                  // Check if it's next day and reversed route
                  const dayDiff = Math.abs(date2 - date1) / (1000 * 60 * 60 * 24);
                  const isNextDay = dayDiff >= 1 && dayDiff <= 2; // Allow 1-2 days difference
                  const isReversedRoute = (
                    mission1.lieu_enlevement === mission2.lieu_acheminement &&
                    mission1.lieu_acheminement === mission2.lieu_enlevement
                  );

                  if (isNextDay && isReversedRoute) {
                    // Found round trip
                    const sortedMissions = date1 < date2 ? [mission1, mission2] : [mission2, mission1];
                    groups.push({
                      isRoundTrip: true,
                      missions: sortedMissions
                    });
                    used.add(i);
                    used.add(j);
                    foundReturn = true;
                    break;
                  }
                }

                if (!foundReturn) {
                  // Single mission
                  groups.push({
                    isRoundTrip: false,
                    missions: [mission1]
                  });
                  used.add(i);
                }
              }

              return groups;
            }

            // Function to calculate curved path between two points
            function calculateCurvedPath(startPoint, endPoint) {
              const lat1 = startPoint[0];
              const lng1 = startPoint[1];
              const lat2 = endPoint[0];
              const lng2 = endPoint[1];

              // Calculate midpoint
              const midLat = (lat1 + lat2) / 2;
              const midLng = (lng1 + lng2) / 2;

              // Calculate distance between points
              const distance = Math.sqrt(Math.pow(lat2 - lat1, 2) + Math.pow(lng2 - lng1, 2));

              // Calculate curve offset (perpendicular to the line)
              const curveOffset = distance * 0.3; // 30% of distance for curve height

              // Calculate perpendicular direction
              const dx = lng2 - lng1;
              const dy = lat2 - lat1;
              const perpLat = -dx / Math.sqrt(dx * dx + dy * dy) * curveOffset;
              const perpLng = dy / Math.sqrt(dx * dx + dy * dy) * curveOffset;

              // Control point for the curve (offset from midpoint)
              const controlLat = midLat + perpLat;
              const controlLng = midLng + perpLng;

              // Generate curved path using quadratic bezier curve
              const pathPoints = [];
              const numPoints = 20; // Number of points for smooth curve

              for (let i = 0; i <= numPoints; i++) {
                const t = i / numPoints;
                const lat = Math.pow(1 - t, 2) * lat1 + 2 * (1 - t) * t * controlLat + Math.pow(t, 2) * lat2;
                const lng = Math.pow(1 - t, 2) * lng1 + 2 * (1 - t) * t * controlLng + Math.pow(t, 2) * lng2;
                pathPoints.push([lat, lng]);
              }

              return pathPoints;
            }

            // Function to display mission arrow on map
            function displayMissionArrow(mission) {
              if (!directMap || !mission.pickup_lat || !mission.pickup_lng || !mission.delivery_lat || !mission.delivery_lng) {
                console.log('Cannot display mission arrow: missing coordinates or map');
                return;
              }

              // Clear existing arrow
              if (missionArrowLayer) {
                directMap.removeLayer(missionArrowLayer);
              }

              const today = new Date().toISOString().split('T')[0];
              const missionDate = mission.rescheduled_date || mission.date;
              const isCurrent = missionDate === today;

              const startPoint = [parseFloat(mission.pickup_lat), parseFloat(mission.pickup_lng)];
              const endPoint = [parseFloat(mission.delivery_lat), parseFloat(mission.delivery_lng)];

              console.log('Arrow coordinates:', {
                start: startPoint,
                end: endPoint,
                pickup_location: mission.lieu_enlevement,
                delivery_location: mission.lieu_acheminement
              });

              // Create curved arrow line
              const arrowOptions = {
                color: isCurrent ? '#00ff00' : '#888888',
                weight: isCurrent ? 3 : 2,
                opacity: isCurrent ? 1 : 0.7,
                dashArray: isCurrent ? null : '10, 10'
              };

              // Calculate curved path points
              const curvedPath = calculateCurvedPath(startPoint, endPoint);
              missionArrowLayer = L.polyline(curvedPath, arrowOptions).addTo(directMap);

              // Add arrow head using a marker at the end of the curved path
              // Calculate angle from the last two points of the curve for proper direction
              const lastPoint = curvedPath[curvedPath.length - 1];
              const secondLastPoint = curvedPath[curvedPath.length - 2];
              const arrowAngle = calculateAngle(secondLastPoint, lastPoint);

              const arrowHeadIcon = L.divIcon({
                className: 'arrow-head',
                html: `<div style="color: ${arrowOptions.color}; font-size: 16px; transform: rotate(${arrowAngle}deg);">▶</div>`,
                iconSize: [20, 20],
                iconAnchor: [10, 10]
              });

              const arrowHead = L.marker(lastPoint, { icon: arrowHeadIcon }).addTo(directMap);

              // Group arrow line and head
              missionArrowLayer = L.layerGroup([missionArrowLayer, arrowHead]).addTo(directMap);

              console.log(`Mission arrow displayed: ${isCurrent ? 'current (green)' : 'future (grey dashed)'}`);
            }

            // Helper function to calculate angle between two points
            function calculateAngle(start, end) {
              const dx = end[1] - start[1];
              const dy = end[0] - start[0];
              return Math.atan2(dy, dx) * 180 / Math.PI;
            }

            // Helper function to format date
            function formatDate(dateString) {
              if (!dateString) return 'Date non définie';
              const date = new Date(dateString);
              return date.toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
              });
            }

            // Function to clear all data layers from the map
            function clearAllMapLayers(map) {
              console.log('Clearing all map layers...');

              // Clear drone layer if it exists
              if (droneLayerGroup) {
                map.removeLayer(droneLayerGroup);
                droneLayerGroup = null;
                droneMarkers = [];
                console.log('Drone layer removed');
              }

              // Clear missile layer if it exists
              if (missileLayerGroup) {
                map.removeLayer(missileLayerGroup);
                missileLayerGroup = null;
                missileMarkers = [];
                console.log('Missile layer removed');
              }

              // Clear mission arrow if it exists
              if (missionArrowLayer) {
                map.removeLayer(missionArrowLayer);
                missionArrowLayer = null;
                console.log('Mission arrow removed');
              }

              // Hide missions panel
              hideMissionsPanel();

              // Add more layer clearing here as needed for future layer types

              // Show the GSA logo overlay when no layers are active
              const mapLogoOverlay = document.getElementById('map-logo-overlay');
              if (mapLogoOverlay) {
                mapLogoOverlay.style.display = 'block';
              }

              // Ensure map is visible and properly sized
              const mapElement = document.getElementById('morocco-map');
              if (mapElement && directMap) {
                // Force the map element to be visible
                mapElement.style.display = 'block';
                mapElement.style.height = '100%';
                mapElement.style.width = '100%';
                mapElement.style.position = 'relative';

                // Invalidate size to fix any rendering issues
                directMap.invalidateSize();

                // Reset view to ensure Morocco is centered
                directMap.setView(initialCenter, initialZoom);
              }

              console.log('All map layers cleared');
            }

            // Function to add a zoom level display to the map
            function addZoomLevelDisplay(map) {
              // Create a custom control that will appear next to the zoom controls
              const zoomDisplay = L.control({ position: 'topleft' });

              // When the control is added to the map
              zoomDisplay.onAdd = function(map) {
                // Create a container for both the reset button and zoom display
                const container = L.DomUtil.create('div', 'map-controls-container');

                // Create the reset button
                const resetButton = L.DomUtil.create('button', 'map-reset-button', container);
                resetButton.innerHTML = '<i class="fas fa-crosshairs"></i>'; // Changed to a crosshairs/target icon for recenter
                resetButton.title = 'Recenter map';

                // Add click event listener to reset button
                L.DomEvent.on(resetButton, 'click', function(e) {
                  L.DomEvent.stopPropagation(e);
                  L.DomEvent.preventDefault(e);

                  // Check if we're in missile view and use appropriate center/zoom
                  let centerCoords, zoomLevel;
                  if (map._currentView === 'missiles') {
                    // Use the exact same constants as missile view
                    centerCoords = MISSILE_VIEW_CENTER;
                    zoomLevel = MISSILE_VIEW_ZOOM;
                    console.log('Reset button: Using missile view settings', centerCoords, zoomLevel);
                  } else {
                    // Use default map settings
                    centerCoords = initialCenter;
                    zoomLevel = initialZoom;
                    console.log('Reset button: Using default view settings', centerCoords, zoomLevel);
                  }

                  // Reset the map view to the appropriate center and zoom
                  map.setView(centerCoords, zoomLevel, {
                    animate: true,
                    duration: 1.0,
                    noMoveStart: true
                  });

                  // Ensure the zoom is exactly the target zoom after a short delay
                  setTimeout(() => {
                    if (map.getZoom() !== zoomLevel) {
                      map.setZoom(zoomLevel);
                      map.fire('zoomend');
                    }
                    console.log('Map view reset to position with zoom level:', map.getZoom());
                  }, 100);
                });

                // Create the zoom level display
                const zoomLevelDisplay = L.DomUtil.create('div', 'zoom-level-display', container);
                zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;

                // Update the zoom level display whenever the zoom changes
                map.on('zoomend', function() {
                  zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;
                });

                // Also update on moveend to catch any zoom changes from other sources
                map.on('moveend', function() {
                  zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;
                });

                // Prevent map click events when clicking the controls
                L.DomEvent.disableClickPropagation(container);

                return container;
              };

              // Add the control to the map
              zoomDisplay.addTo(map);
            }

            // Reset button functionality has been integrated into the zoom level display function

            // Function to set up map toggle buttons
            function setupMapToggleButtons() {
              const toggleButtons = document.querySelectorAll('.map-toggle-btn');
              const mapLogoOverlay = document.getElementById('map-logo-overlay');

              // Function to ensure map is visible and properly sized
              function ensureMapIsVisible() {
                // Get the map element
                const mapElement = document.getElementById('morocco-map');
                if (!mapElement) return;

                // Force the map element to be visible
                mapElement.style.display = 'block';
                mapElement.style.height = '100%';
                mapElement.style.width = '100%';
                mapElement.style.position = 'relative';

                // Invalidate size to fix any rendering issues
                if (directMap) {
                  console.log('Refreshing map size');
                  directMap.invalidateSize();

                  // Reset view to ensure Morocco is centered
                  directMap.setView(initialCenter, initialZoom);
                }
              }

              // Function to check if any toggle button is active
              function isAnyToggleActive() {
                return Array.from(toggleButtons).some(btn => btn.classList.contains('active'));
              }

              // Function to update logo visibility
              function updateLogoVisibility() {
                if (isAnyToggleActive()) {
                  // Hide logo when any toggle is active
                  mapLogoOverlay.style.display = 'none';
                } else {
                  // Show logo when no toggle is active
                  mapLogoOverlay.style.display = 'block';
                }
              }

              // Initial setup - ensure map is visible and show logo if no toggle is active
              ensureMapIsVisible();
              updateLogoVisibility();

              // Add click event listeners to each button
              toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                  const layer = this.getAttribute('data-layer');
                  const isCurrentlyActive = this.classList.contains('active');

                  // First, remove active class from all buttons
                  toggleButtons.forEach(btn => {
                    btn.classList.remove('active');
                  });

                  // First, clear all layers from the map regardless of which button was clicked
                  clearAllMapLayers(directMap);

                  // Ensure map is visible
                  ensureMapIsVisible();

                  // If the clicked button wasn't already active, make it active and display its data
                  if (!isCurrentlyActive) {
                    this.classList.add('active');
                    console.log(`Toggle ${layer} layer: ON`);

                    // Handle different layer types
                    switch(layer) {
                      case 'missiles':
                        // Fetch and display missile deployments
                        fetchAndDisplayMissileDeployments(directMap);
                        // Show missions panel and fetch mission data
                        showMissionsPanel();
                        break;
                      case 'drones':
                        // Fetch and display drone deployments
                        fetchAndDisplayDroneDeployments(directMap);
                        break;
                      case 'personnel':
                        console.log('Personnel drone layer not implemented yet');
                        // Restore default map settings for personnel view
                        restoreDefaultMapSettings(directMap);
                        break;
                      case 'stats':
                        console.log('Statistics layer not implemented yet');
                        // Restore default map settings for statistics view
                        restoreDefaultMapSettings(directMap);
                        break;
                      default:
                        console.log(`Unknown layer type: ${layer}`);
                    }
                  } else {
                    // If the button was already active, just turn it off
                    // The map is already cleared at the beginning of this function
                    console.log(`Toggle ${layer} layer: OFF`);
                  }

                  // Update logo visibility based on toggle state
                  updateLogoVisibility();
                });
              });

              console.log('Map toggle buttons initialized');
            }

            // No need for a popup here as it's handled in the GeoJSON implementation

            // Map bounds are set in the GeoJSON implementation

            // Add click handler to map tab to refresh the map
            document.querySelectorAll('.tab').forEach(function (tab) {
              const tabName = tab.getAttribute('data-tab');

              if (tabName === 'map') {
                tab.addEventListener('click', function () {
                  console.log('Map tab clicked in direct script');
                  setTimeout(function () {
                    if (directMap) {
                      console.log('Refreshing map size in direct script');

                      // Ensure map is visible and properly sized
                      const mapElement = document.getElementById('morocco-map');
                      if (mapElement) {
                        // Force the map element to be visible
                        mapElement.style.display = 'block';
                        mapElement.style.height = '100%';
                        mapElement.style.width = '100%';
                        mapElement.style.position = 'relative';
                      }

                      // Invalidate size to fix any rendering issues
                      directMap.invalidateSize();

                      // Reset view to ensure Morocco is centered
                      directMap.setView(initialCenter, initialZoom);

                      // Update logo visibility based on toggle button state
                      const mapLogoOverlay = document.getElementById('map-logo-overlay');
                      const isAnyToggleActive = Array.from(document.querySelectorAll('.map-toggle-btn')).some(btn => btn.classList.contains('active'));

                      if (mapLogoOverlay) {
                        mapLogoOverlay.style.display = isAnyToggleActive ? 'none' : 'block';
                      }

                      // Refresh drone positions disabled as requested
                      console.log('Drone fetching and display disabled as requested');
                    }
                  }, 300);
                });
              } else {
                // For other tabs, clear the drone refresh interval when switching away from map
                tab.addEventListener('click', function() {
                  if (droneRefreshInterval) {
                    console.log('Clearing drone refresh interval');
                    clearInterval(droneRefreshInterval);
                    droneRefreshInterval = null;
                  }
                });
              }
            });
          } catch (error) {
            console.error('Error in direct map initialization:', error);
          }
        }, 2000);
      });
    </script>

    <!-- Activity Details Modal -->
    <div id="activity-details-modal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 id="modal-activity-title">Détails de l'activité</h2>
          <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
          <!-- Activity navigation for multiple activities on same date -->
          <div id="modal-activity-nav" class="activity-nav">
            <!-- Will be populated dynamically -->
          </div>

          <div class="activity-details">
            <div class="detail-row">
              <div class="detail-label">Activité:</div>
              <div id="modal-activity-name" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Date de début:</div>
              <div id="modal-activity-start-date" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Date de fin:</div>
              <div id="modal-activity-end-date" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Durée:</div>
              <div id="modal-activity-duration" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Lieu:</div>
              <div id="modal-activity-location" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Référence:</div>
              <div id="modal-activity-reference" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Observations:</div>
              <div id="modal-activity-observations" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Source:</div>
              <div id="modal-activity-source" class="detail-value"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal pour tous les stagiaires de la formation -->
    <div id="all-trainees-modal" class="modal">
      <div class="modal-content all-trainees-modal-content">
        <div class="modal-header">
          <h2 id="all-trainees-modal-title">Tous les stagiaires - Formation</h2>
          <span class="close" id="close-all-trainees-modal">&times;</span>
        </div>
        <div class="modal-body">
          <!-- Barre de recherche et filtres -->
          <div class="modal-search-container">
            <div class="search-bar-modal">
              <input type="text" id="all-trainees-search" placeholder="Rechercher par nom, prénom, grade, unité, fonction..." />
              <button type="button" id="clear-search-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <!-- Filtre par instance -->
            <div class="instance-filter-container">
              <select id="all-trainees-instance-filter" class="instance-filter">
                <option value="">Toutes les instances</option>
              </select>
            </div>
            <!-- Filtre par fonction -->
            <div class="fonction-filter-container">
              <select id="all-trainees-fonction-filter" class="fonction-filter">
                <option value="">Toutes les fonctions</option>
              </select>
            </div>
          </div>
          <!-- Conteneur de la table -->
          <div class="all-trainees-table-container" id="all-trainees-table-container">
            <div class="loading-message">Chargement des stagiaires...</div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
