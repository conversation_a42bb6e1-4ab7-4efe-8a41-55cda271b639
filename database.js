const mysql = require('mysql2/promise');
const { ipcMain } = require('electron');

// Database configuration
// These settings can be moved to a configuration file later
const dbConfig = {
  host: 'localhost', // This can be changed to the server IP when deployed
  user: 'root',
  password: '065950',
  database: 'gsa',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
};

// Create a connection pool
let pool = null;

// Initialize the database connection
async function initDatabase() {
  try {
    pool = mysql.createPool(dbConfig);

    // Test the connection
    const connection = await pool.getConnection();

    // Create and populate the drones_deployment table if needed
    try {
      await ensureDroneDeploymentTable(connection);
    } catch (tableError) {
      console.error('Error setting up drones_deployment table:', tableError);
      // Continue even if table setup fails
    }

    connection.release();

    console.log('Database connection established successfully');
    return { connected: true, error: null };
  } catch (error) {
    console.error('Failed to connect to the database:', error);
    return { connected: false, error: error.message };
  }
}

// Ensure the drones_deployment table exists and has sample data
async function ensureDroneDeploymentTable(connection) {
  try {
    console.log('Checking for drones_deployment table...');

    // Check if the table exists
    const [tables] = await connection.query(`
      SELECT TABLE_NAME
      FROM information_schema.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'drones_deployment'
    `, [dbConfig.database]);

    // If the table doesn't exist, create it
    if (tables.length === 0) {
      console.log('Creating drones_deployment table...');

      // Create the table
      await connection.query(`
        CREATE TABLE IF NOT EXISTS \`drones_deployment\` (
          \`id\` int(11) NOT NULL AUTO_INCREMENT,
          \`lat\` decimal(10,6) NOT NULL,
          \`lng\` decimal(10,6) NOT NULL,
          \`type_drone\` varchar(50) NOT NULL,
          \`deployment_date\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (\`id\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);

      // Insert sample data
      await insertSampleDroneData(connection);
    } else {
      // Check if the table has data
      const [count] = await connection.query('SELECT COUNT(*) as count FROM drones_deployment');

      if (count[0].count === 0) {
        console.log('drones_deployment table exists but is empty, adding sample data...');
        await insertSampleDroneData(connection);
      } else {
        console.log(`drones_deployment table exists with ${count[0].count} records`);
      }
    }
  } catch (error) {
    console.error('Error ensuring drones_deployment table:', error);
    throw error;
  }
}

// Insert sample drone data
async function insertSampleDroneData(connection) {
  try {
    console.log('Inserting sample drone data...');

    // Surveillance drones
    await connection.query(`
      INSERT INTO \`drones_deployment\` (\`lat\`, \`lng\`, \`type_drone\`) VALUES
      (31.629472, -7.992047, 'Surveillance'),
      (33.573109, -7.589843, 'Surveillance'),
      (34.020882, -6.841650, 'Surveillance'),
      (35.169359, -5.268970, 'Surveillance'),
      (30.427755, -9.598107, 'Surveillance');
    `);

    // Reconnaissance drones
    await connection.query(`
      INSERT INTO \`drones_deployment\` (\`lat\`, \`lng\`, \`type_drone\`) VALUES
      (32.336957, -6.360626, 'Reconnaissance'),
      (34.684947, -1.900873, 'Reconnaissance'),
      (31.047901, -4.017334, 'Reconnaissance'),
      (28.448454, -11.118584, 'Reconnaissance'),
      (29.698242, -7.981808, 'Reconnaissance');
    `);

    // Attack drones
    await connection.query(`
      INSERT INTO \`drones_deployment\` (\`lat\`, \`lng\`, \`type_drone\`) VALUES
      (33.879136, -5.554810, 'Attack'),
      (31.508345, -5.129395, 'Attack'),
      (35.005566, -2.631836, 'Attack'),
      (27.940283, -12.885742, 'Attack'),
      (32.687031, -4.438477, 'Attack');
    `);

    // Transport drones
    await connection.query(`
      INSERT INTO \`drones_deployment\` (\`lat\`, \`lng\`, \`type_drone\`) VALUES
      (34.043310, -5.000000, 'Transport'),
      (32.323925, -9.238281, 'Transport'),
      (30.145127, -6.547852, 'Transport'),
      (35.460670, -5.000000, 'Transport'),
      (33.211116, -8.222656, 'Transport');
    `);

    // Medical drones
    await connection.query(`
      INSERT INTO \`drones_deployment\` (\`lat\`, \`lng\`, \`type_drone\`) VALUES
      (34.597042, -5.888672, 'Medical'),
      (33.063924, -6.811523, 'Medical'),
      (31.802893, -7.294922, 'Medical'),
      (35.317366, -3.999023, 'Medical'),
      (29.840644, -9.404297, 'Medical');
    `);

    console.log('Sample drone data inserted successfully');
  } catch (error) {
    console.error('Error inserting sample drone data:', error);
    throw error;
  }
}

// Check if the database is connected
async function checkConnection() {
  if (!pool) {
    return { connected: false, error: 'Database pool not initialized' };
  }

  try {
    const connection = await pool.getConnection();
    connection.release();
    return { connected: true, error: null };
  } catch (error) {
    console.error('Database connection check failed:', error);
    return { connected: false, error: error.message };
  }
}

// Execute a query
async function query(sql, params) {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const [results] = await pool.execute(sql, params);
    return results;
  } catch (error) {
    console.error('Query error:', error);
    throw error;
  }
}

// Close the database connection
async function closeDatabase() {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('Database connection closed');
  }
}

// Periodic connection check
let connectionCheckInterval = null;
let lastConnectionStatus = { connected: false, error: null };
let mainWindow = null;

// Start periodic connection check
function startConnectionMonitoring(window, intervalMs = 10000) {
  // Store reference to main window
  mainWindow = window;

  // Clear any existing interval
  if (connectionCheckInterval) {
    clearInterval(connectionCheckInterval);
  }

  // Initial check
  performConnectionCheck();

  // Set up interval for periodic checks
  connectionCheckInterval = setInterval(performConnectionCheck, intervalMs);

  console.log(
    `Database connection monitoring started (interval: ${intervalMs}ms)`
  );
}

// Stop periodic connection check
function stopConnectionMonitoring() {
  if (connectionCheckInterval) {
    clearInterval(connectionCheckInterval);
    connectionCheckInterval = null;
    console.log('Database connection monitoring stopped');
  }
}

// Perform a connection check and notify if status changed
async function performConnectionCheck() {
  try {
    // Check connection
    const status = await checkConnection();

    // If status changed, notify the renderer
    if (
      mainWindow &&
      (status.connected !== lastConnectionStatus.connected ||
        status.error !== lastConnectionStatus.error)
    ) {
      console.log(
        `Database connection status changed: ${
          status.connected ? 'Connected' : 'Disconnected'
        }`
      );
      mainWindow.webContents.send('database-status', status);
      lastConnectionStatus = status;
    }

    return status;
  } catch (error) {
    console.error('Error during connection check:', error);
    const status = { connected: false, error: error.message };

    // If status changed, notify the renderer
    if (
      mainWindow &&
      (status.connected !== lastConnectionStatus.connected ||
        status.error !== lastConnectionStatus.error)
    ) {
      console.log('Database connection status changed: Disconnected (error)');
      mainWindow.webContents.send('database-status', status);
      lastConnectionStatus = status;
    }

    return status;
  }
}

// Get all drone deployments
async function getDroneDeployments() {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    // Use a simple query to ensure we get all records
    const connection = await pool.getConnection();

    try {
      // First, let's check how many records we have
      const [countResult] = await connection.query('SELECT COUNT(*) as count FROM drones_deployment');
      const totalCount = countResult[0].count;
      console.log(`Total drone records in database: ${totalCount}`);

      // Now get all the records with the updated table structure
      const [results] = await connection.query(`
        SELECT
          id,
          unité,
          lat,
          lng,
          type_drone,
          ville_implantation
        FROM
          drones_deployment
      `);
      console.log(`Database returned ${results.length} drone records:`, JSON.stringify(results));

      // Transform the results to ensure consistent format
      const transformedResults = results.map(record => ({
        id: record.id,
        unite: record.unité,
        lat: typeof record.lat === 'string' ? parseFloat(record.lat) : record.lat,
        lng: typeof record.lng === 'string' ? parseFloat(record.lng) : record.lng,
        type_drone: record.type_drone,
        ville_implantation: record.ville_implantation
      }));

      // Log each record for debugging
      transformedResults.forEach((drone, index) => {
        console.log(`Drone ${index + 1}:`, drone);
      });

      // If we didn't get any results but we know there should be records, try a direct query
      if (transformedResults.length === 0 && totalCount > 0) {
        console.log('No results returned but count is non-zero. Trying direct query...');
        return await getDirectDroneDeployments(connection);
      }

      return transformedResults;
    } finally {
      // Always release the connection
      connection.release();
    }
  } catch (error) {
    console.error('Error fetching drone deployments:', error);

    // If there's an error, try the direct query as a fallback
    try {
      console.log('Trying direct query as fallback...');
      return await getDirectDroneDeployments();
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      throw error; // Throw the original error
    }
  }
}

// Get drone deployments using a direct query approach
async function getDirectDroneDeployments(existingConnection = null) {
  try {
    const connection = existingConnection || await pool.getConnection();

    try {
      // Use a very simple query with explicit column selection for the updated table structure
      const query = `
        SELECT
          id,
          unité,
          lat,
          lng,
          type_drone,
          ville_implantation
        FROM
          drones_deployment
      `;

      console.log('Executing direct query:', query);
      const [results] = await connection.query(query);
      console.log(`Direct query returned ${results.length} drone records`);

      // Transform the results to ensure consistent format
      const transformedResults = results.map(record => ({
        id: record.id,
        unite: record.unité,
        lat: typeof record.lat === 'string' ? parseFloat(record.lat) : record.lat,
        lng: typeof record.lng === 'string' ? parseFloat(record.lng) : record.lng,
        type_drone: record.type_drone,
        ville_implantation: record.ville_implantation
      }));

      console.log('Transformed results:', JSON.stringify(transformedResults));
      return transformedResults;
    } finally {
      // Only release if we created a new connection
      if (!existingConnection) {
        connection.release();
      }
    }
  } catch (error) {
    console.error('Error in direct drone query:', error);
    throw error;
  }
}

// Get training statistics for a specific domain
async function getTrainingStatisticsByDomain(domain) {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      // Create an object to store the statistics
      const statistics = {};

      let totalQuery, totalParams, typeQuery, typeParams, yearlyQuery, yearlyParams;

      if (domain === 'tout') {
        // Query for all domains
        totalQuery = `
          SELECT COUNT(*) as total
          FROM schedule_trainings
        `;
        totalParams = [];

        typeQuery = `
          SELECT type, COUNT(*) as count
          FROM schedule_trainings
          GROUP BY type
          ORDER BY count DESC
        `;
        typeParams = [];

        yearlyQuery = `
          SELECT YEAR(du) as year, COUNT(*) as count
          FROM schedule_trainings
          WHERE du IS NOT NULL
          GROUP BY YEAR(du)
          ORDER BY year
        `;
        yearlyParams = [];
      } else {
        // Query for specific domain
        totalQuery = `
          SELECT COUNT(*) as total
          FROM schedule_trainings
          WHERE domaine = ?
        `;
        totalParams = [domain];

        typeQuery = `
          SELECT type, COUNT(*) as count
          FROM schedule_trainings
          WHERE domaine = ?
          GROUP BY type
          ORDER BY count DESC
        `;
        typeParams = [domain];

        yearlyQuery = `
          SELECT YEAR(du) as year, COUNT(*) as count
          FROM schedule_trainings
          WHERE domaine = ? AND du IS NOT NULL
          GROUP BY YEAR(du)
          ORDER BY year
        `;
        yearlyParams = [domain];
      }

      // Execute queries
      const [totalTrainingCount] = await connection.query(totalQuery, totalParams);
      const [trainingsByType] = await connection.query(typeQuery, typeParams);
      const [yearlyResults] = await connection.query(yearlyQuery, yearlyParams);

      statistics.total = totalTrainingCount[0].total;
      statistics.byType = trainingsByType;
      statistics.currentDomain = domain;
      statistics.yearlyData = {
        byDomain: {
          [domain]: yearlyResults
        },
        allYears: yearlyResults.map(r => r.year)
      };

      console.log(`Training statistics for domain '${domain}':`, statistics);
      return statistics;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching training statistics for domain '${domain}':`, error);
    throw error;
  }
}

// Get trainees for a specific training instance
async function getTraineesForTraining(trainingId) {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      // Get trainees for the specified training instance
      const [trainees] = await connection.query(`
        SELECT
          id,
          grade,
          nom,
          prenom,
          mle,
          unite,
          fonction
        FROM
          stagiaires
        WHERE
          formation_id = ?
        ORDER BY
          grade, nom, prenom
      `, [trainingId]);

      console.log(`Retrieved ${trainees.length} trainees for training ID ${trainingId}`);
      return trainees;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching trainees for training ID ${trainingId}:`, error);
    throw error;
  }
}

// Get radied trainees for a specific training instance
async function getRadiedTrainees(trainingId) {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      // Get radied trainees for the specified training instance
      const [radiedTrainees] = await connection.query(`
        SELECT
          id_stagiaires,
          id_formation,
          reference,
          motif
        FROM
          radies
        WHERE
          id_formation = ?
      `, [trainingId]);

      console.log(`Retrieved ${radiedTrainees.length} radied trainees for training ID ${trainingId}`);
      return radiedTrainees;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching radied trainees for training ID ${trainingId}:`, error);
    throw error;
  }
}

// Get total number of trainees for all instances of a training
async function getTotalTraineesForTrainingName(trainingName) {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      console.log(`Fetching total trainees for training: ${trainingName}`);

      // First, let's get all training instances for this training name
      // Use schedule_trainings table which has the nom_formation column
      const [instances] = await connection.query(`
        SELECT id, nom_formation
        FROM schedule_trainings
        WHERE nom_formation = ?
      `, [trainingName]);

      console.log(`Found ${instances.length} instances for training: ${trainingName}`);

      if (instances.length === 0) {
        console.log(`No instances found for training: ${trainingName}`);
        return 0;
      }

      // Get the IDs of all instances
      const instanceIds = instances.map(instance => instance.id);
      console.log(`Instance IDs: ${instanceIds.join(', ')}`);

      // Now count trainees for all these instances
      const placeholders = instanceIds.map(() => '?').join(',');
      const [result] = await connection.query(`
        SELECT COUNT(DISTINCT id) as total_trainees
        FROM stagiaires
        WHERE formation_id IN (${placeholders})
      `, instanceIds);

      const totalTrainees = result[0]?.total_trainees || 0;
      console.log(`Retrieved total of ${totalTrainees} trainees for training: ${trainingName}`);
      return totalTrainees;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching total trainees for training ${trainingName}:`, error);
    throw error;
  }
}

// Get total number of radied trainees for all instances of a training
async function getTotalRadiedTraineesForTrainingName(trainingName) {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      console.log(`Fetching total radied trainees for training: ${trainingName}`);

      // First, let's get all training instances for this training name
      const [instances] = await connection.query(`
        SELECT id, nom_formation
        FROM schedule_trainings
        WHERE nom_formation = ?
      `, [trainingName]);

      console.log(`Found ${instances.length} instances for training: ${trainingName}`);

      if (instances.length === 0) {
        console.log(`No instances found for training: ${trainingName}`);
        return 0;
      }

      // Get the IDs of all instances
      const instanceIds = instances.map(instance => instance.id);
      console.log(`Instance IDs: ${instanceIds.join(', ')}`);

      // Now count radied trainees for all these instances
      const placeholders = instanceIds.map(() => '?').join(',');
      const [result] = await connection.query(`
        SELECT COUNT(DISTINCT id_stagiaires) as total_radied_trainees
        FROM radies
        WHERE id_formation IN (${placeholders})
      `, instanceIds);

      const totalRadiedTrainees = result[0]?.total_radied_trainees || 0;
      console.log(`Retrieved total of ${totalRadiedTrainees} radied trainees for training: ${trainingName}`);
      return totalRadiedTrainees;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching total radied trainees for training ${trainingName}:`, error);
    throw error;
  }
}

// Get all trainees for all instances of a training
async function getAllTraineesForTrainingName(trainingName) {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      console.log(`Fetching all trainees for training: ${trainingName}`);

      // First, get all training instances for this training name
      const [instances] = await connection.query(`
        SELECT id, nom_formation, tranche
        FROM schedule_trainings
        WHERE nom_formation = ?
      `, [trainingName]);

      console.log(`Found ${instances.length} instances for training: ${trainingName}`);

      if (instances.length === 0) {
        console.log(`No instances found for training: ${trainingName}`);
        return { success: true, data: [] };
      }

      // Get the IDs of all instances
      const instanceIds = instances.map(instance => instance.id);
      console.log(`Instance IDs: ${instanceIds.join(', ')}`);

      // Get all trainees for these instances
      const placeholders = instanceIds.map(() => '?').join(',');
      const [trainees] = await connection.query(`
        SELECT
          s.id,
          s.grade,
          s.nom,
          s.prenom,
          s.mle,
          s.unite,
          s.fonction,
          s.formation_id,
          st.tranche,
          st.du,
          st.au,
          st.extended_to,
          st.rescheduled_du,
          st.rescheduled_au
        FROM
          stagiaires s
        LEFT JOIN
          schedule_trainings st ON s.formation_id = st.id
        WHERE
          s.formation_id IN (${placeholders})
        ORDER BY
          s.formation_id, s.fonction, s.grade, s.nom, s.prenom
      `, instanceIds);

      console.log(`Retrieved ${trainees.length} trainees for training: ${trainingName}`);

      // Get radied trainees for these instances
      const [radiedTrainees] = await connection.query(`
        SELECT
          id_stagiaires,
          id_formation,
          reference,
          motif
        FROM
          radies
        WHERE
          id_formation IN (${placeholders})
      `, instanceIds);

      console.log(`Retrieved ${radiedTrainees.length} radied trainees for training: ${trainingName}`);

      // Create a map of radied trainees for quick lookup
      const radiedMap = {};
      radiedTrainees.forEach(radied => {
        radiedMap[radied.id_stagiaires] = {
          reference: radied.reference,
          motif: radied.motif
        };
      });

      // Mark radied trainees
      const traineesWithRadiedStatus = trainees.map(trainee => ({
        ...trainee,
        isRadied: !!radiedMap[trainee.id],
        radiedInfo: radiedMap[trainee.id] || null
      }));

      console.log(`Processed ${traineesWithRadiedStatus.length} trainees with radied status for training: ${trainingName}`);
      return { success: true, data: traineesWithRadiedStatus };
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching all trainees for training ${trainingName}:`, error);
    return { success: false, error: error.message };
  }
}

// Set up IPC handlers for database operations
function setupIpcHandlers() {
  // Check database connection
  ipcMain.handle('check-database-connection', async () => {
    return await performConnectionCheck();
  });

  // Get drone deployments
  ipcMain.handle('get-drone-deployments', async () => {
    try {
      const deployments = await getDroneDeployments();
      return { success: true, data: deployments };
    } catch (error) {
      console.error('Error in get-drone-deployments handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get trainees for a specific training instance
  ipcMain.handle('get-trainees-for-training', async (event, trainingId) => {
    try {
      const trainees = await getTraineesForTraining(trainingId);
      return { success: true, data: trainees };
    } catch (error) {
      console.error('Error in get-trainees-for-training handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get radied trainees for a specific training instance
  ipcMain.handle('get-radied-trainees', async (event, trainingId) => {
    try {
      const radiedTrainees = await getRadiedTrainees(trainingId);
      return { success: true, data: radiedTrainees };
    } catch (error) {
      console.error('Error in get-radied-trainees handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get total number of trainees for all instances of a training
  ipcMain.handle('get-total-trainees-for-training-name', async (event, trainingName) => {
    try {
      const totalTrainees = await getTotalTraineesForTrainingName(trainingName);
      return { success: true, data: totalTrainees };
    } catch (error) {
      console.error('Error in get-total-trainees-for-training-name handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get total number of radied trainees for all instances of a training
  ipcMain.handle('get-total-radied-trainees-for-training-name', async (event, trainingName) => {
    try {
      const totalRadiedTrainees = await getTotalRadiedTraineesForTrainingName(trainingName);
      return { success: true, data: totalRadiedTrainees };
    } catch (error) {
      console.error('Error in get-total-radied-trainees-for-training-name handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get missile deployments
  ipcMain.handle('get-missile-deployments', async () => {
    try {
      const deployments = await getMissileDeployments();
      return { success: true, data: deployments };
    } catch (error) {
      console.error('Error in get-missile-deployments handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get mission verification missiles
  ipcMain.handle('get-mission-verification-missiles', async () => {
    try {
      const missions = await getMissionVerificationMissiles();
      return { success: true, data: missions };
    } catch (error) {
      console.error('Error in get-mission-verification-missiles handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get training schedules
  ipcMain.handle('get-training-schedules', async () => {
    try {
      const schedules = await getTrainingSchedules();
      return { success: true, data: schedules };
    } catch (error) {
      console.error('Error in get-training-schedules handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Add more IPC handlers for database operations as needed

  // Get activities timeline data
  ipcMain.handle('get-activities-timeline', async () => {
    try {
      const activities = await getActivitiesTimeline();
      return { success: true, data: activities };
    } catch (error) {
      console.error('Error in get-activities-timeline handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get statistics for KPI dashboard
  ipcMain.handle('get-statistics', async () => {
    try {
      const statistics = await getStatistics();
      return { success: true, data: statistics };
    } catch (error) {
      console.error('Error in get-statistics handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get training statistics for a specific domain
  ipcMain.handle('get-training-statistics-by-domain', async (event, domain) => {
    try {
      console.log(`Handling request for training statistics for domain: ${domain}`);
      const statistics = await getTrainingStatisticsByDomain(domain);
      return { success: true, data: statistics };
    } catch (error) {
      console.error(`Error in get-training-statistics-by-domain handler for domain '${domain}':`, error);
      return { success: false, error: error.message };
    }
  });

  // Get trainee statistics for a specific domain
  ipcMain.handle('get-trainee-statistics-by-domain', async (event, domain) => {
    try {
      console.log(`Handling request for trainee statistics for domain: ${domain}`);
      const statistics = await getTraineeStatisticsByDomain(domain);
      return { success: true, data: statistics };
    } catch (error) {
      console.error(`Error in get-trainee-statistics-by-domain handler for domain '${domain}':`, error);
      return { success: false, error: error.message };
    }
  });

  // Get formations by domain with last instance dates
  ipcMain.handle('get-formations-by-domain', async (event, domain) => {
    try {
      console.log(`Handling request for formations by domain: ${domain}`);
      const formations = await getFormationsByDomain(domain);
      console.log(`Formations for domain '${domain}':`, formations);
      return { success: true, data: formations };
    } catch (error) {
      console.error(`Error in get-formations-by-domain handler for domain '${domain}':`, error);
      return { success: false, error: error.message };
    }
  });

  // Get unique trainings
  ipcMain.handle('get-unique-trainings', async (event, domaine) => {
    try {
      return await getUniqueTrainings(domaine);
    } catch (error) {
      console.error('Error in get-unique-trainings handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get training details
  ipcMain.handle('get-training-details', async (event, nomFormation) => {
    try {
      return await getTrainingDetails(nomFormation);
    } catch (error) {
      console.error('Error in get-training-details handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get all instances of a training
  ipcMain.handle('get-training-instances', async (event, nomFormation) => {
    try {
      return await getTrainingInstances(nomFormation);
    } catch (error) {
      console.error('Error in get-training-instances handler:', error);
      return { success: false, error: error.message };
    }
  });

  // Get all trainees for all instances of a training
  ipcMain.handle('get-all-trainees-for-training', async (event, trainingName) => {
    try {
      return await getAllTraineesForTrainingName(trainingName);
    } catch (error) {
      console.error('Error in get-all-trainees-for-training handler:', error);
      return { success: false, error: error.message };
    }
  });
}

// Get missile module deployments
async function getMissileDeployments() {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    // Use a simple query to ensure we get all records
    const connection = await pool.getConnection();

    try {
      // First, let's check how many records we have
      const [countResult] = await connection.query('SELECT COUNT(*) as count FROM modules_deployment');
      const totalCount = countResult[0].count;
      console.log(`Total missile module records in database: ${totalCount}`);

      // Now get all the records with the new table structure
      const [results] = await connection.query(`
        SELECT
          id,
          \`Unité\` as unite,
          Type,
          Materiel,
          \`Type_missile\` as type_missile,
          \`Batterie/module\` as batterie_module,
          \`Unité_accueillante\` as unite_accueillante,
          \`Type_unité_accueillante\` as type_unite_accueillante,
          \`Ville d'implantation\` as ville_implantation,
          Lat as lat,
          Lng as lng
        FROM
          modules_deployment
      `);
      console.log(`Database returned ${results.length} missile module records`);

      // Transform the results to ensure consistent format with new table structure
      const transformedResults = results.map(record => ({
        id: record.id,
        unite: record.unite,
        type: record.Type,
        materiel: record.Materiel,
        type_missile: record.type_missile,
        batterie_module: record.batterie_module,
        unite_accueillante: record.unite_accueillante,
        type_unite_accueillante: record.type_unite_accueillante,
        ville_implantation: record.ville_implantation,
        lat: typeof record.lat === 'string' ? parseFloat(record.lat) : record.lat,
        lng: typeof record.lng === 'string' ? parseFloat(record.lng) : record.lng
      }));

      return transformedResults;
    } finally {
      // Always release the connection
      connection.release();
    }
  } catch (error) {
    console.error('Error fetching missile deployments:', error);
    throw error;
  }
}

// Get mission verification missiles (current and future only)
async function getMissionVerificationMissiles() {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      // First check if the table exists
      const checkTableQuery = `
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'mission_verification_missiles'
      `;

      const [tableCheck] = await connection.execute(checkTableQuery);
      console.log('Table existence check result:', tableCheck[0]);

      if (tableCheck[0].table_exists === 0) {
        console.log('ERROR: Table mission_verification_missiles does not exist in database');
        return [];
      }

      console.log('Table mission_verification_missiles exists, proceeding with query');

      // First, let's check what columns exist in modules_deployment
      const columnsQuery = `
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'modules_deployment'
      `;

      const [columns] = await connection.execute(columnsQuery);
      console.log('Available columns in modules_deployment:', columns.map(c => c.COLUMN_NAME));

      const query = `
        SELECT
          m.id,
          m.type,
          m.date,
          m.rescheduled_date,
          m.obs,
          m.mission,
          m.id_lieu_enlevement,
          m.id_lieu_acheminement,
          CASE
            WHEN m.id_lieu_enlevement = 0 THEN 'Sidi Yahya'
            WHEN m.id_lieu_enlevement > 0 THEN pickup.\`Ville d'implantation\`
            ELSE 'Location inconnue'
          END as lieu_enlevement,
          CASE
            WHEN m.id_lieu_enlevement = 0 THEN 34.30494
            WHEN pickup.\`Ville d'implantation\` = 'Salé' THEN 34.0337
            ELSE pickup.lat
          END as pickup_lat,
          CASE
            WHEN m.id_lieu_enlevement = 0 THEN -6.30404
            WHEN pickup.\`Ville d'implantation\` = 'Salé' THEN -6.8416
            ELSE pickup.lng
          END as pickup_lng,
          CASE
            WHEN m.id_lieu_acheminement = 0 THEN 'Sidi Yahya'
            WHEN m.id_lieu_acheminement > 0 THEN delivery.\`Ville d'implantation\`
            ELSE 'Location inconnue'
          END as lieu_acheminement,
          CASE
            WHEN m.id_lieu_acheminement = 0 THEN 34.30494
            WHEN delivery.\`Ville d'implantation\` = 'Salé' THEN 34.0337
            ELSE delivery.lat
          END as delivery_lat,
          CASE
            WHEN m.id_lieu_acheminement = 0 THEN -6.30404
            WHEN delivery.\`Ville d'implantation\` = 'Salé' THEN -6.8416
            ELSE delivery.lng
          END as delivery_lng,
          -- Pickup location details
          pickup.\`Batterie/module\` as pickup_batterie_module,
          pickup.\`Unité\` as pickup_unite,
          pickup.Type as pickup_type,
          pickup.\`Unité_accueillante\` as pickup_unite_accueillante,
          pickup.\`Type_unité_accueillante\` as pickup_type_unite_accueillante,
          -- Delivery location details
          delivery.\`Batterie/module\` as delivery_batterie_module,
          delivery.\`Unité\` as delivery_unite,
          delivery.Type as delivery_type,
          delivery.\`Unité_accueillante\` as delivery_unite_accueillante,
          delivery.\`Type_unité_accueillante\` as delivery_type_unite_accueillante
        FROM mission_verification_missiles m
        LEFT JOIN modules_deployment pickup ON m.id_lieu_enlevement = pickup.id AND m.id_lieu_enlevement > 0
        LEFT JOIN modules_deployment delivery ON m.id_lieu_acheminement = delivery.id AND m.id_lieu_acheminement > 0
        -- WHERE COALESCE(m.rescheduled_date, m.date) >= CURDATE()
        ORDER BY COALESCE(m.rescheduled_date, m.date) ASC
      `;

      console.log('Executing main query:', query);
      const [results] = await connection.execute(query);
      console.log(`Fetched ${results.length} mission verification missiles with date filter`);
      console.log('Filtered mission data:', results);

      // Let's check ALL missions without any filter to debug
      const debugQuery = `
        SELECT
          m.id,
          m.type,
          m.date,
          m.rescheduled_date,
          m.obs,
          m.id_lieu_enlevement,
          m.id_lieu_acheminement
        FROM mission_verification_missiles m
        ORDER BY m.id ASC
      `;

      const [allMissions] = await connection.execute(debugQuery);
      console.log(`Total missions in database: ${allMissions.length}`);
      console.log('All missions (raw data):', allMissions);

      // Test date comparison with actual mission dates
      const dateTestQuery = `
        SELECT
          CURDATE() as today,
          '2025-08-27' as test_future_date,
          '2025-08-27' >= CURDATE() as is_future,
          '2025-02-24' as test_past_date,
          '2025-02-24' >= CURDATE() as is_past_future,
          COUNT(*) as total_missions,
          SUM(CASE WHEN COALESCE(m.rescheduled_date, m.date) >= CURDATE() THEN 1 ELSE 0 END) as future_missions,
          SUM(CASE WHEN COALESCE(m.rescheduled_date, m.date) < CURDATE() THEN 1 ELSE 0 END) as past_missions
        FROM mission_verification_missiles m
      `;

      const [dateTest] = await connection.execute(dateTestQuery);
      console.log('Date comparison and mission count test:', dateTest);

      return results;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error fetching mission verification missiles:', error);
    throw error;
  }
}

// Get current training schedules (those active on the current date)
async function getTrainingSchedules() {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    // Use a query that filters for current trainings
    const connection = await pool.getConnection();

    try {
      // Get current date in local format (YYYY-MM-DD)
      const now = new Date();
      const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      console.log(`Fetching trainings active on: ${currentDate} (local date)`);

      // Also create a Date object with time set to 00:00:00 for consistent comparison
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);

      // Query for trainings that are currently active
      // A training is active if the current date is between 'du' and 'au' (or extended_to if present)
      const [results] = await connection.query(`
        SELECT
          id,
          domaine,
          nom_formation,
          tranche,
          du,
          au,
          extended_to
        FROM
          schedule_trainings
        WHERE
          ? >= DATE(du) AND ? <= COALESCE(DATE(extended_to), DATE(au))
        ORDER BY
          du ASC
      `, [currentDate, currentDate]);

      // Log the SQL query for debugging
      console.log(`SQL Query: SELECT * FROM schedule_trainings WHERE '${currentDate}' >= DATE(du) AND '${currentDate}' <= COALESCE(DATE(extended_to), DATE(au))`);
      console.log(`Query parameters: ${currentDate}`);

      // Log each training record for debugging
      if (results.length > 0) {
        console.log("Current trainings found:");
        results.forEach((training, index) => {
          console.log(`Training ${index + 1}: ${training.nom_formation}`);
          console.log(`  Start date: ${training.du}`);
          console.log(`  End date: ${training.au || training.extended_to}`);

          // Verify if this training is actually current
          // Handle dates properly whether they are Date objects or strings
          let startDate, endDate;

          // Handle start date
          if (training.du instanceof Date) {
            // If it's already a Date object, use it directly
            startDate = new Date(training.du);
          } else if (typeof training.du === 'string') {
            // If it's a string, parse it
            if (training.du.includes('-')) {
              // YYYY-MM-DD format
              const duParts = training.du.split('-').map(part => parseInt(part, 10));
              startDate = new Date(duParts[0], duParts[1] - 1, duParts[2], 0, 0, 0, 0);
            } else {
              // Other format, let JavaScript handle it
              startDate = new Date(training.du);
            }
          } else {
            // Fallback
            startDate = new Date(training.du);
          }

          // Handle end date
          const endDateValue = training.au || training.extended_to;
          if (endDateValue instanceof Date) {
            // If it's already a Date object, use it directly
            endDate = new Date(endDateValue);
          } else if (typeof endDateValue === 'string') {
            // If it's a string, parse it
            if (endDateValue.includes('-')) {
              // YYYY-MM-DD format
              const auParts = endDateValue.split('-').map(part => parseInt(part, 10));
              endDate = new Date(auParts[0], auParts[1] - 1, auParts[2], 0, 0, 0, 0);
            } else {
              // Other format, let JavaScript handle it
              endDate = new Date(endDateValue);
            }
          } else {
            // Fallback
            endDate = new Date(endDateValue);
          }

          // Set time components to 0 for consistent comparison
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(0, 0, 0, 0);

          // Use the today variable we created earlier with time set to 00:00:00
          const isActuallyCurrent = today >= startDate && today <= endDate;

          console.log(`  Is actually current: ${isActuallyCurrent}`);
          console.log(`  Today: ${today.toDateString()}, Start: ${startDate.toDateString()}, End: ${endDate.toDateString()}`);
          console.log(`  Today (local): ${formatLocalDate(today)}, Start (local): ${formatLocalDate(startDate)}, End (local): ${formatLocalDate(endDate)}`);
        });
      } else {
        console.log("No current trainings found");
      }

      console.log(`Database returned ${results.length} current training schedules`);

      // Transform the results to ensure consistent format
      const transformedResults = results.map(record => ({
        id: record.id,
        domaine: record.domaine,
        nom_formation: record.nom_formation,
        tranche: record.tranche,
        du: record.du,
        au: record.au || record.extended_to, // Use extended_to as end date if present
        extended_to: record.extended_to
      }));

      return transformedResults;
    } finally {
      // Always release the connection
      connection.release();
    }
  } catch (error) {
    console.error('Error fetching current training schedules:', error);
    throw error;
  }
}

// Get activities timeline data from multiple tables
async function getActivitiesTimeline() {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      // Get current date in MySQL format (YYYY-MM-DD)
      const currentDate = new Date().toISOString().split('T')[0];
      console.log(`Fetching activities for timeline on: ${currentDate}`);

      // Query for activities from all three tables
      const [gsaResults] = await connection.query(`
        SELECT
          id,
          activité,
          du,
          au,
          lieu,
          obs,
          reference,
          'GSA' as source
        FROM
          activites_gsa
        ORDER BY
          du ASC
      `);

      const [driResults] = await connection.query(`
        SELECT
          id,
          activité,
          du,
          au,
          lieu,
          obs,
          reference,
          'DRI' as source
        FROM
          activites_dri
        ORDER BY
          du ASC
      `);

      const [missionsResults] = await connection.query(`
        SELECT
          id,
          activité,
          du,
          au,
          lieu,
          obs,
          reference,
          'Mission' as source
        FROM
          missions_gsa
        ORDER BY
          du ASC
      `);

      // Combine all results
      const allActivities = [
        ...gsaResults,
        ...driResults,
        ...missionsResults
      ];

      // Sort by date
      allActivities.sort((a, b) => {
        const dateA = new Date(a.du);
        const dateB = new Date(b.du);
        return dateA - dateB;
      });

      console.log(`Combined ${allActivities.length} activities for timeline`);

      // Log each activity's date for debugging
      allActivities.forEach((activity, index) => {
        console.log(`\n========== DETAILED DATE ANALYSIS FOR ACTIVITY ${index + 1} ==========`);
        console.log(`Activity: "${activity.activité}"`);

        // Log the raw date value from the database
        console.log(`\nRAW DATE FROM DATABASE:`);
        console.log(`- Value: ${activity.du}`);
        console.log(`- Type: ${typeof activity.du}`);
        console.log(`- Constructor: ${activity.du.constructor ? activity.du.constructor.name : 'N/A'}`);

        // If it's a Date object, log detailed information
        if (activity.du instanceof Date) {
          console.log(`\nDETAILS AS DATE OBJECT (BEFORE ANY PROCESSING):`);
          console.log(`- toString(): ${activity.du.toString()}`);
          console.log(`- toISOString(): ${activity.du.toISOString()}`);
          console.log(`- toUTCString(): ${activity.du.toUTCString()}`);
          console.log(`- toLocaleDateString(): ${activity.du.toLocaleDateString()}`);
          console.log(`- getFullYear(): ${activity.du.getFullYear()}`);
          console.log(`- getMonth(): ${activity.du.getMonth()} (0=January, 11=December)`);
          console.log(`- getDate(): ${activity.du.getDate()}`);
          console.log(`- getDay(): ${activity.du.getDay()} (0=Sunday, 1=Monday, ..., 6=Saturday)`);
          console.log(`- getHours(): ${activity.du.getHours()}`);
          console.log(`- getMinutes(): ${activity.du.getMinutes()}`);
          console.log(`- getTimezoneOffset(): ${activity.du.getTimezoneOffset()} minutes`);
        }

        // Create a JavaScript Date object to see how it's interpreted
        console.log(`\nAFTER CREATING NEW DATE OBJECT FROM THE VALUE:`);
        const jsDate = new Date(activity.du);
        console.log(`- toString(): ${jsDate.toString()}`);
        console.log(`- toISOString(): ${jsDate.toISOString()}`);
        console.log(`- toUTCString(): ${jsDate.toUTCString()}`);
        console.log(`- toLocaleDateString(): ${jsDate.toLocaleDateString()}`);
        console.log(`- getFullYear(): ${jsDate.getFullYear()}`);
        console.log(`- getMonth(): ${jsDate.getMonth()} (0=January, 11=December)`);
        console.log(`- getDate(): ${jsDate.getDate()}`);
        console.log(`- getDay(): ${jsDate.getDay()} (0=Sunday, 1=Monday, ..., 6=Saturday)`);
        console.log(`- getHours(): ${jsDate.getHours()}`);
        console.log(`- getMinutes(): ${jsDate.getMinutes()}`);
        console.log(`- getTimezoneOffset(): ${jsDate.getTimezoneOffset()} minutes`);

        // Try creating a date with explicit time component
        console.log(`\nCREATING DATE WITH EXPLICIT TIME COMPONENT:`);
        let explicitDate;
        if (typeof activity.du === 'string') {
          // If it's a string, try to parse it
          if (activity.du.includes('T')) {
            // Already has time component
            explicitDate = new Date(activity.du);
          } else if (activity.du.includes('-')) {
            // YYYY-MM-DD format
            explicitDate = new Date(`${activity.du}T00:00:00`);
          } else if (activity.du.includes('/')) {
            // DD/MM/YYYY format
            const parts = activity.du.split('/');
            if (parts.length === 3) {
              explicitDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}T00:00:00`);
            } else {
              explicitDate = new Date(activity.du);
            }
          } else {
            explicitDate = new Date(activity.du);
          }
        } else if (activity.du instanceof Date) {
          // If it's already a Date, create a new one with explicit time
          const year = activity.du.getFullYear();
          const month = activity.du.getMonth() + 1;
          const day = activity.du.getDate();
          explicitDate = new Date(`${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T00:00:00`);
        } else {
          explicitDate = new Date(activity.du);
        }

        console.log(`- toString(): ${explicitDate.toString()}`);
        console.log(`- toISOString(): ${explicitDate.toISOString()}`);
        console.log(`- getDay(): ${explicitDate.getDay()} (0=Sunday, 1=Monday, ..., 6=Saturday)`);
        console.log(`=================================================================\n`);
      });

      // Transform the results to ensure consistent format
      const transformedResults = allActivities.map(record => ({
        id: record.id,
        activite: record.activité,
        du: record.du,
        au: record.au,
        lieu: record.lieu,
        obs: record.obs,
        reference: record.reference || '', // Include reference field, default to empty string if null
        source: record.source
      }));

      return transformedResults;
    } finally {
      // Always release the connection
      connection.release();
    }
  } catch (error) {
    console.error('Error fetching activities timeline data:', error);
    throw error;
  }
}

// Helper function to format a date as YYYY-MM-DD using local date components
function formatLocalDate(date) {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

// Get statistics for KPI dashboard
async function getStatistics() {
  console.log('getStatistics function called');
  if (!pool) {
    console.error('Database pool not initialized');
    throw new Error('Database not initialized');
  }

  try {
    console.log('Getting database connection');
    const connection = await pool.getConnection();

    try {
      // Create an object to store all statistics
      const statistics = {};

      // Get current date for all date-based calculations
      const now = new Date();
      console.log('Current date for statistics:', now.toISOString());

      // 1. Count total number of drones by type
      console.log('Fetching drone statistics');
      const [droneStats] = await connection.query(`
        SELECT
          type_drone,
          COUNT(*) as count
        FROM
          drones_deployment
        GROUP BY
          type_drone
      `);
      statistics.drones = droneStats;
      console.log('Drone statistics:', droneStats);

      // 2. Count total number of missile modules
      console.log('Fetching missile statistics');
      const [missileCount] = await connection.query(`
        SELECT
          COUNT(*) as total,
          COUNT(DISTINCT type_missile) as types
        FROM
          modules_deployment
      `);
      statistics.missiles = missileCount[0];
      console.log('Missile statistics:', missileCount[0]);

      // 3. Count total trainings and trainings by type
      console.log('Fetching training statistics');

      // Get distinct training domains
      const [domains] = await connection.query(`
        SELECT DISTINCT
          domaine
        FROM
          schedule_trainings
        WHERE
          domaine IS NOT NULL AND domaine != ''
        ORDER BY
          domaine ASC
      `);

      statistics.trainingDomains = domains.map(record => record.domaine);
      console.log('Training domains:', statistics.trainingDomains);

      // Default domain is 'Drone'
      const defaultDomain = 'Drone';

      // Get total number of trainings for the default domain
      const [totalTrainingCount] = await connection.query(`
        SELECT
          COUNT(*) as total
        FROM
          schedule_trainings
        WHERE
          domaine = ?
      `, [defaultDomain]);

      // Get trainings by type for the default domain
      const [trainingsByType] = await connection.query(`
        SELECT
          type,
          COUNT(*) as count
        FROM
          schedule_trainings
        WHERE
          domaine = ?
        GROUP BY
          type
      `, [defaultDomain]);

      // Get total trainings for each domain
      const [trainingsByDomain] = await connection.query(`
        SELECT
          domaine,
          COUNT(*) as count
        FROM
          schedule_trainings
        WHERE
          domaine IS NOT NULL AND domaine != ''
        GROUP BY
          domaine
        ORDER BY
          domaine ASC
      `);

      // ===== YEARLY TRAINING COMPARISON =====
      // Get all years with training data grouped by year and domain
      // Exclure les formations dont la date "du" est NULL pour le graphique d'évolution
      const [yearlyTrainingsByDomain] = await connection.query(`
        SELECT
          domaine,
          YEAR(du) as year,
          COUNT(*) as count
        FROM
          schedule_trainings
        WHERE
          domaine IS NOT NULL AND domaine != ''
          AND du IS NOT NULL
        GROUP BY
          domaine, YEAR(du)
        ORDER BY
          domaine, YEAR(du)
      `);

      // Process the data to organize by domain and year
      const trainingsByDomainAndYear = {};
      const yearsList = new Set();

      // First pass: collect all years and initialize domain data
      yearlyTrainingsByDomain.forEach(record => {
        const domain = record.domaine;
        const year = record.year;
        const count = record.count;

        yearsList.add(year);

        if (!trainingsByDomainAndYear[domain]) {
          trainingsByDomainAndYear[domain] = {};
        }

        trainingsByDomainAndYear[domain][year] = count;
      });

      // Convert to sorted array
      const sortedYears = Array.from(yearsList).sort();

      // Calculate percentage changes for each domain
      const domainPercentChanges = {};

      Object.keys(trainingsByDomainAndYear).forEach(domain => {
        const yearData = trainingsByDomainAndYear[domain];
        const years = Object.keys(yearData).map(Number).sort();

        // Check if we have at least 2 years of data to calculate percentage change
        if (years.length >= 2) {
          const latestYear = years[years.length - 1];
          const previousYear = years[years.length - 2];

          const latestCount = yearData[latestYear];
          const previousCount = yearData[previousYear];

          let percentChange = 0;
          if (previousCount > 0) {
            percentChange = ((latestCount - previousCount) / previousCount) * 100;
          } else if (latestCount > 0) {
            percentChange = 100; // If previous year was 0, and current year has trainings, that's a 100% increase
          }

          domainPercentChanges[domain] = {
            percentChange: percentChange.toFixed(1),
            hasSufficientData: years.length >= 3 // At least 3 years of data is considered sufficient
          };
        } else {
          domainPercentChanges[domain] = {
            percentChange: "0.0",
            hasSufficientData: false
          };
        }
      });

      // We don't need monthly comparison anymore, as per requirements

      // Get current year for reference
      const currentYear = now.getFullYear();

      statistics.trainings = {
        total: totalTrainingCount[0].total,
        byType: trainingsByType,
        byDomain: trainingsByDomain,
        currentDomain: defaultDomain,
        yearlyData: {
          byDomain: trainingsByDomainAndYear,
          allYears: Array.from(yearsList).sort(),
          domainPercentChanges: domainPercentChanges
        }
      };

      console.log('Training statistics:', statistics.trainings);

      // 4. Count activities for current week and previous week, and current month and previous month
      console.log('Fetching activities by week and month');

      // Format dates for SQL
      const formatDate = (date) => `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

      // ===== WEEKLY COMPARISON =====
      // Get current week start and end dates
      const currentWeekStart = new Date(now);
      currentWeekStart.setDate(now.getDate() - now.getDay() + (now.getDay() === 0 ? -6 : 1)); // Monday of current week
      currentWeekStart.setHours(0, 0, 0, 0);

      const currentWeekEnd = new Date(currentWeekStart);
      currentWeekEnd.setDate(currentWeekStart.getDate() + 6); // Sunday of current week
      currentWeekEnd.setHours(23, 59, 59, 999);

      // Get previous week start and end dates
      const prevWeekStart = new Date(currentWeekStart);
      prevWeekStart.setDate(currentWeekStart.getDate() - 7); // Monday of previous week

      const prevWeekEnd = new Date(prevWeekStart);
      prevWeekEnd.setDate(prevWeekStart.getDate() + 6); // Sunday of previous week
      prevWeekEnd.setHours(23, 59, 59, 999);

      const currentWeekStartStr = formatDate(currentWeekStart);
      const currentWeekEndStr = formatDate(currentWeekEnd);
      const prevWeekStartStr = formatDate(prevWeekStart);
      const prevWeekEndStr = formatDate(prevWeekEnd);

      console.log('Current week:', currentWeekStartStr, 'to', currentWeekEndStr);
      console.log('Previous week:', prevWeekStartStr, 'to', prevWeekEndStr);

      // Count current week activities from all three tables
      const [currentGsaActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_gsa
        WHERE du BETWEEN ? AND ?
      `, [currentWeekStartStr, currentWeekEndStr]);

      const [currentDriActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_dri
        WHERE du BETWEEN ? AND ?
      `, [currentWeekStartStr, currentWeekEndStr]);

      const [currentMissionActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM missions_gsa
        WHERE du BETWEEN ? AND ?
      `, [currentWeekStartStr, currentWeekEndStr]);

      // Count previous week activities from all three tables
      const [prevGsaActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_gsa
        WHERE du BETWEEN ? AND ?
      `, [prevWeekStartStr, prevWeekEndStr]);

      const [prevDriActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_dri
        WHERE du BETWEEN ? AND ?
      `, [prevWeekStartStr, prevWeekEndStr]);

      const [prevMissionActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM missions_gsa
        WHERE du BETWEEN ? AND ?
      `, [prevWeekStartStr, prevWeekEndStr]);

      // Calculate totals for weekly comparison
      const currentWeekTotal = currentGsaActivities[0].count + currentDriActivities[0].count + currentMissionActivities[0].count;
      const prevWeekTotal = prevGsaActivities[0].count + prevDriActivities[0].count + prevMissionActivities[0].count;

      // Calculate percentage change for weekly comparison
      let weeklyPercentChange = 0;
      if (prevWeekTotal > 0) {
        weeklyPercentChange = ((currentWeekTotal - prevWeekTotal) / prevWeekTotal) * 100;
      } else if (currentWeekTotal > 0) {
        weeklyPercentChange = 100; // If previous week was 0, and current week has activities, that's a 100% increase
      }

      // ===== MONTHLY COMPARISON =====
      // Get current month start and end dates
      const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      currentMonthEnd.setHours(23, 59, 59, 999);

      // Get previous month start and end dates
      const prevMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const prevMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
      prevMonthEnd.setHours(23, 59, 59, 999);

      const currentMonthStartStr = formatDate(currentMonthStart);
      const currentMonthEndStr = formatDate(currentMonthEnd);
      const prevMonthStartStr = formatDate(prevMonthStart);
      const prevMonthEndStr = formatDate(prevMonthEnd);

      console.log('Current month:', currentMonthStartStr, 'to', currentMonthEndStr);
      console.log('Previous month:', prevMonthStartStr, 'to', prevMonthEndStr);

      // Count current month activities from all three tables
      const [currentMonthGsaActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_gsa
        WHERE du BETWEEN ? AND ?
      `, [currentMonthStartStr, currentMonthEndStr]);

      const [currentMonthDriActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_dri
        WHERE du BETWEEN ? AND ?
      `, [currentMonthStartStr, currentMonthEndStr]);

      const [currentMonthMissionActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM missions_gsa
        WHERE du BETWEEN ? AND ?
      `, [currentMonthStartStr, currentMonthEndStr]);

      // Count previous month activities from all three tables
      const [prevMonthGsaActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_gsa
        WHERE du BETWEEN ? AND ?
      `, [prevMonthStartStr, prevMonthEndStr]);

      const [prevMonthDriActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_dri
        WHERE du BETWEEN ? AND ?
      `, [prevMonthStartStr, prevMonthEndStr]);

      const [prevMonthMissionActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM missions_gsa
        WHERE du BETWEEN ? AND ?
      `, [prevMonthStartStr, prevMonthEndStr]);

      // Calculate totals for monthly comparison
      const currentMonthTotal = currentMonthGsaActivities[0].count + currentMonthDriActivities[0].count + currentMonthMissionActivities[0].count;
      const prevMonthTotal = prevMonthGsaActivities[0].count + prevMonthDriActivities[0].count + prevMonthMissionActivities[0].count;

      // Calculate percentage change for monthly comparison
      let monthlyPercentChange = 0;
      if (prevMonthTotal > 0) {
        monthlyPercentChange = ((currentMonthTotal - prevMonthTotal) / prevMonthTotal) * 100;
      } else if (currentMonthTotal > 0) {
        monthlyPercentChange = 100; // If previous month was 0, and current month has activities, that's a 100% increase
      }

      // For the next 30 days (keep this for the original count)
      const thirtyDaysLater = new Date(now);
      thirtyDaysLater.setDate(now.getDate() + 30);
      const futureDate = formatDate(thirtyDaysLater);
      const currentDate = formatDate(now);

      // Count from all three activity tables for the next 30 days
      const [gsaActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_gsa
        WHERE du BETWEEN ? AND ?
      `, [currentDate, futureDate]);

      const [driActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM activites_dri
        WHERE du BETWEEN ? AND ?
      `, [currentDate, futureDate]);

      const [missionActivities] = await connection.query(`
        SELECT COUNT(*) as count FROM missions_gsa
        WHERE du BETWEEN ? AND ?
      `, [currentDate, futureDate]);

      statistics.upcomingActivities = {
        total: gsaActivities[0].count + driActivities[0].count + missionActivities[0].count,
        gsa: gsaActivities[0].count,
        dri: driActivities[0].count,
        missions: missionActivities[0].count,
        weeklyComparison: {
          currentWeek: {
            total: currentWeekTotal,
            start: currentWeekStartStr,
            end: currentWeekEndStr,
            gsa: currentGsaActivities[0].count,
            dri: currentDriActivities[0].count,
            missions: currentMissionActivities[0].count
          },
          previousWeek: {
            total: prevWeekTotal,
            start: prevWeekStartStr,
            end: prevWeekEndStr,
            gsa: prevGsaActivities[0].count,
            dri: prevDriActivities[0].count,
            missions: prevMissionActivities[0].count
          },
          percentChange: weeklyPercentChange.toFixed(1)
        },
        monthlyComparison: {
          currentMonth: {
            total: currentMonthTotal,
            start: currentMonthStartStr,
            end: currentMonthEndStr,
            gsa: currentMonthGsaActivities[0].count,
            dri: currentMonthDriActivities[0].count,
            missions: currentMonthMissionActivities[0].count
          },
          previousMonth: {
            total: prevMonthTotal,
            start: prevMonthStartStr,
            end: prevMonthEndStr,
            gsa: prevMonthGsaActivities[0].count,
            dri: prevMonthDriActivities[0].count,
            missions: prevMonthMissionActivities[0].count
          },
          percentChange: monthlyPercentChange.toFixed(1)
        }
      };

      console.log('Activities statistics:', statistics.upcomingActivities);
      console.log('All statistics retrieved successfully:', statistics);
      return statistics;

    } finally {
      connection.release();
      console.log('Database connection released');
    }
  } catch (error) {
    console.error('Error fetching statistics:', error);
    throw error;
  }
}

// Fonction pour récupérer les détails d'une formation spécifique
async function getTrainingDetails(nomFormation) {
  try {
    console.log(`Fetching details for training: ${nomFormation}...`);
    const connection = await pool.getConnection();

    try {
      // Récupérer les détails de la formation
      const [details] = await connection.query(`
        SELECT
          *
        FROM
          schedule_trainings
        WHERE
          nom_formation = ?
        LIMIT 1
      `, [nomFormation]);

      if (details.length === 0) {
        console.log(`No details found for training: ${nomFormation}`);
        return { success: false, error: 'Formation non trouvée' };
      }

      console.log(`Retrieved details for training: ${nomFormation}`);
      return { success: true, data: details[0] };
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching details for training: ${nomFormation}`, error);
    return { success: false, error: error.message };
  }
}

// Fonction pour récupérer toutes les instances d'une formation
async function getTrainingInstances(nomFormation) {
  try {
    console.log(`Fetching all instances for training: ${nomFormation}...`);
    const connection = await pool.getConnection();

    try {
      // Récupérer toutes les instances de la formation
      // Utiliser DESCRIBE pour obtenir les colonnes existantes
      const [columns] = await connection.query(`DESCRIBE schedule_trainings`);
      console.log('Available columns in schedule_trainings:', columns.map(col => col.Field));

      // Récupérer toutes les instances de la formation avec les colonnes existantes
      const [instances] = await connection.query(`
        SELECT
          id,
          nom_formation,
          tranche,
          du,
          au,
          extended_to,
          rescheduled_du,
          rescheduled_au,
          domaine,
          type
        FROM
          schedule_trainings
        WHERE
          nom_formation = ?
        ORDER BY
          CASE
            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du
            ELSE du
          END DESC
      `, [nomFormation]);

      if (instances.length === 0) {
        console.log(`No instances found for training: ${nomFormation}`);
        return { success: false, error: 'Aucune instance trouvée' };
      }

      console.log(`Retrieved ${instances.length} instances for training: ${nomFormation}`);
      return { success: true, data: instances };
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching instances for training: ${nomFormation}`, error);
    return { success: false, error: error.message };
  }
}

// Fonction pour récupérer les formations uniques (sans doublons)
async function getUniqueTrainings(domaine = null) {
  try {
    console.log(`Fetching unique trainings${domaine ? ` for domain: ${domaine}` : ''}...`);
    const connection = await pool.getConnection();

    try {
      // Récupérer les formations uniques avec le nombre d'instances et la date de fin la plus récente
      let query = `
        SELECT
          nom_formation,
          domaine,
          COUNT(*) as instance_count,
          MAX(
            CASE
              WHEN rescheduled_au IS NOT NULL THEN rescheduled_au
              WHEN extended_to IS NOT NULL THEN extended_to
              ELSE au
            END
          ) as last_end_date
        FROM
          schedule_trainings
        WHERE
          nom_formation IS NOT NULL AND nom_formation != ''
      `;

      // Ajouter la condition de domaine si spécifiée
      const params = [];
      if (domaine && domaine !== 'Tout') {
        query += ` AND domaine = ?`;
        params.push(domaine);
      }

      // Ajouter le GROUP BY pour compter correctement les instances et s'assurer que les noms de formation sont uniques
      query += ` GROUP BY nom_formation`;

      // Ajouter l'ordre par date de fin la plus récente (descendant) pour que les formations les plus récentes apparaissent en haut
      query += ` ORDER BY last_end_date DESC, nom_formation ASC`;

      // Exécuter la requête
      const [trainings] = await connection.query(query, params);

      // Récupérer également tous les domaines distincts pour le filtre
      const [domains] = await connection.query(`
        SELECT DISTINCT
          domaine
        FROM
          schedule_trainings
        WHERE
          domaine IS NOT NULL AND domaine != ''
        ORDER BY
          domaine ASC
      `);

      // Récupérer le nombre de formations par domaine
      const [domainCounts] = await connection.query(`
        SELECT
          domaine,
          COUNT(DISTINCT nom_formation) as count
        FROM
          schedule_trainings
        WHERE
          domaine IS NOT NULL AND domaine != ''
          AND nom_formation IS NOT NULL AND nom_formation != ''
        GROUP BY
          domaine
      `);

      // Créer un objet avec les compteurs par domaine
      const domainCountsMap = {};
      domainCounts.forEach(item => {
        domainCountsMap[item.domaine] = item.count;
      });

      // Calculer le nombre total de formations uniques
      const [totalCount] = await connection.query(`
        SELECT
          COUNT(DISTINCT nom_formation) as total
        FROM
          schedule_trainings
        WHERE
          nom_formation IS NOT NULL AND nom_formation != ''
      `);

      const totalTrainings = totalCount[0].total;

      console.log(`Retrieved ${trainings.length} unique trainings and ${domains.length} domains with counts`);
      return {
        success: true,
        data: trainings,
        domains: domains.map(d => d.domaine),
        domainCounts: domainCountsMap,
        totalCount: totalTrainings
      };
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error fetching unique trainings:', error);
    return { success: false, error: error.message };
  }
}

// Function to get formations by domain with last instance dates
async function getFormationsByDomain(domain) {
  try {
    console.log(`Fetching formations for domain: ${domain}`);
    const connection = await pool.getConnection();

    try {
      let query, params;

      if (domain === 'tout') {
        // Query for all domains
        query = `
          SELECT
            nom_formation as nom,
            domaine,
            COUNT(*) as instanceCount,
            MAX(COALESCE(extended_to, au)) as lastInstanceDate
          FROM schedule_trainings
          WHERE nom_formation IS NOT NULL AND nom_formation != ''
          GROUP BY nom_formation, domaine
          ORDER BY lastInstanceDate DESC, nom_formation ASC
        `;
        params = [];
      } else {
        // Query for specific domain
        query = `
          SELECT
            nom_formation as nom,
            domaine,
            COUNT(*) as instanceCount,
            MAX(COALESCE(extended_to, au)) as lastInstanceDate
          FROM schedule_trainings
          WHERE domaine = ? AND nom_formation IS NOT NULL AND nom_formation != ''
          GROUP BY nom_formation, domaine
          ORDER BY lastInstanceDate DESC, nom_formation ASC
        `;
        params = [domain];
      }

      const [result] = await connection.query(query, params);

      console.log(`Found ${result.length} formations for domain '${domain}'`);
      return result;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching formations for domain '${domain}':`, error);
    throw error;
  }
}

// Get trainee statistics for a specific domain
async function getTraineeStatisticsByDomain(domain) {
  if (!pool) {
    throw new Error('Database not initialized');
  }

  try {
    const connection = await pool.getConnection();

    try {
      // Create an object to store the statistics
      const statistics = {};

      let totalQuery, totalParams, typeQuery, typeParams, yearlyQuery, yearlyParams, uniteQuery, uniteParams;

      if (domain === 'tout') {
        // Query for all domains - count trainees from stagiaires table
        totalQuery = `
          SELECT COUNT(*) as total
          FROM stagiaires s
          INNER JOIN schedule_trainings st ON s.formation_id = st.id
        `;
        totalParams = [];

        typeQuery = `
          SELECT st.type, COUNT(s.id) as count
          FROM stagiaires s
          INNER JOIN schedule_trainings st ON s.formation_id = st.id
          GROUP BY st.type
          ORDER BY count DESC
        `;
        typeParams = [];

        yearlyQuery = `
          SELECT YEAR(st.du) as year, COUNT(s.id) as count
          FROM stagiaires s
          INNER JOIN schedule_trainings st ON s.formation_id = st.id
          WHERE st.du IS NOT NULL
          GROUP BY YEAR(st.du)
          ORDER BY year
        `;
        yearlyParams = [];

        uniteQuery = `
          SELECT s.unite, COUNT(s.id) as count
          FROM stagiaires s
          INNER JOIN schedule_trainings st ON s.formation_id = st.id
          WHERE s.unite IS NOT NULL AND s.unite != ''
          GROUP BY s.unite
          ORDER BY count DESC
          LIMIT 3
        `;
        uniteParams = [];
      } else {
        // Query for specific domain - count trainees from stagiaires table
        totalQuery = `
          SELECT COUNT(*) as total
          FROM stagiaires s
          INNER JOIN schedule_trainings st ON s.formation_id = st.id
          WHERE st.domaine = ?
        `;
        totalParams = [domain];

        typeQuery = `
          SELECT st.type, COUNT(s.id) as count
          FROM stagiaires s
          INNER JOIN schedule_trainings st ON s.formation_id = st.id
          WHERE st.domaine = ?
          GROUP BY st.type
          ORDER BY count DESC
        `;
        typeParams = [domain];

        yearlyQuery = `
          SELECT YEAR(st.du) as year, COUNT(s.id) as count
          FROM stagiaires s
          INNER JOIN schedule_trainings st ON s.formation_id = st.id
          WHERE st.domaine = ? AND st.du IS NOT NULL
          GROUP BY YEAR(st.du)
          ORDER BY year
        `;
        yearlyParams = [domain];

        uniteQuery = `
          SELECT s.unite, COUNT(s.id) as count
          FROM stagiaires s
          INNER JOIN schedule_trainings st ON s.formation_id = st.id
          WHERE st.domaine = ? AND s.unite IS NOT NULL AND s.unite != ''
          GROUP BY s.unite
          ORDER BY count DESC
          LIMIT 3
        `;
        uniteParams = [domain];
      }

      // Execute queries
      const [totalTraineeCount] = await connection.query(totalQuery, totalParams);
      const [traineesByType] = await connection.query(typeQuery, typeParams);
      const [yearlyResults] = await connection.query(yearlyQuery, yearlyParams);
      const [traineesByUnite] = await connection.query(uniteQuery, uniteParams);

      statistics.total = totalTraineeCount[0].total;
      statistics.byType = traineesByType;
      statistics.byUnite = traineesByUnite;
      statistics.currentDomain = domain;
      statistics.yearlyData = {
        byDomain: {
          [domain]: yearlyResults
        },
        allYears: yearlyResults.map(r => r.year)
      };

      console.log(`Trainee statistics for domain '${domain}':`, statistics);
      return statistics;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(`Error fetching trainee statistics for domain '${domain}':`, error);
    throw error;
  }
}

// Le gestionnaire IPC pour récupérer les formations uniques est maintenant intégré dans setupIpcHandlers

module.exports = {
  initDatabase,
  checkConnection,
  query,
  closeDatabase,
  setupIpcHandlers,
  startConnectionMonitoring,
  stopConnectionMonitoring,
  getDroneDeployments,
  getDirectDroneDeployments,
  getMissileDeployments,
  getMissionVerificationMissiles,
  getTrainingSchedules,
  getActivitiesTimeline,
  getStatistics,
  getTrainingStatisticsByDomain,
  getTraineeStatisticsByDomain,
  getFormationsByDomain,
  getUniqueTrainings,
  getTrainingInstances,
};
